<?php
session_start();
if (!isset($_SESSION['central_status_result'])) {
    header('Location: add_status.php');
    exit;
}
$data = $_SESSION['central_status_result'];
$devices = $data['devices'];
$statuses = $data['statuses'];
$notes = $data['notes'];
$time = $data['time'];

// حفظ البيانات في قاعدة البيانات
include '../config/db.php';
$success = true;
$error_message = '';

try {
    // التحقق من وجود الجدول وإنشائه إذا لم يكن موجوداً
    $table_check = $conn->query("SHOW TABLES LIKE 'central_status'");
    if (!$table_check || $table_check->num_rows == 0) {
        $create_table = "CREATE TABLE IF NOT EXISTS central_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device VARCHAR(32) NOT NULL,
            status VARCHAR(32) NOT NULL,
            note VARCHAR(255) DEFAULT NULL,
            sent_at DATETIME NOT NULL,
            sender_name VARCHAR(100) DEFAULT NULL
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

        if (!$conn->query($create_table)) {
            throw new Exception("خطأ في إنشاء جدول قاعدة البيانات: " . $conn->error);
        }
    }

    // بدء المعاملة
    $conn->begin_transaction();

    // إعداد الاستعلام
    $sender_name = isset($_SESSION['user']['ar_name']) ? $_SESSION['user']['ar_name'] : 'مخابر الاتصالات المركزية';
    $stmt = $conn->prepare("INSERT INTO central_status (sent_at, device, status, note, sender_name) VALUES (?, ?, ?, ?, ?)");

    if (!$stmt) {
        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
    }

    foreach ($devices as $device) {
        $device_status = $statuses[$device] ?? '';
        $device_note = $notes[$device] ?? '';

        if (!empty($device_status)) {
            $stmt->bind_param("sssss", $time, $device, $device_status, $device_note, $sender_name);
            if (!$stmt->execute()) {
                throw new Exception("خطأ في حفظ بيانات الجهاز: " . $device . " - " . $stmt->error);
            }
        }
    }

    // تأكيد المعاملة
    $conn->commit();
    $stmt->close();

} catch (Exception $e) {
    // إلغاء المعاملة في حالة الخطأ
    if ($conn->ping()) {
        $conn->rollback();
    }
    $success = false;
    $error_message = $e->getMessage();
}

$conn->close();
unset($_SESSION['central_status_result']);
include '../includes/header.php';
renderHeader("تأكيد الإرسال", true, "index.php");
?>

<style>
    .confirm-section {
        background: #fff;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(30,60,114,0.13);
        padding: 38px 22px 28px 22px;
        margin: 48px auto 0 auto;
        max-width: 850px;
        border: 1.5px solid #1da1f2;
        text-align: center;
    }
    .confirm-section h3 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 18px;
    }
    .confirm-section .time {
        color: #28a745;
        font-size: 1.1em;
        margin-bottom: 18px;
    }
    .confirm-table {
        margin: 0 auto 18px auto;
        width: 100%;
        background: #f7fafd;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(29,161,242,0.07);
    }
    .confirm-table th {
        background: #1da1f2;
        color: #fff;
        font-size: 1.05em;
        font-weight: bold;
    }
    .confirm-table td {
        color: #232526;
        background: #eaf6fb;
        font-size: 1em;
    }
    .confirm-table tr:nth-child(even) td {
        background: #d6eaf8;
    }
</style>

<div class="confirm-section">
    <?php if ($success) {
        // حساب عدد الأجهزة المحفوظة
        $saved_count = 0;
        foreach ($devices as $device) {
            if (!empty($statuses[$device] ?? '')) {
                $saved_count++;
            }
        }
    ?>
        <h3>✅ تم حفظ موقف الاتصالات بنجاح</h3>
        <div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <strong>تم حفظ البيانات بنجاح!</strong><br>
            عدد الأجهزة المحفوظة: <?= $saved_count ?> جهاز<br>
            الوقت: <?= htmlspecialchars($time) ?>
        </div>
    <?php } else { ?>
        <h3>❌ خطأ في حفظ البيانات</h3>
        <div class="alert alert-danger" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <strong>حدث خطأ:</strong><br>
            <?= htmlspecialchars($error_message) ?><br>
            <small>يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني</small>
        </div>
    <?php } ?>
    <div class="time">وقت الإرسال: <?= htmlspecialchars($time) ?></div>
    <table class="confirm-table">
        <thead>
            <tr>
                <th>#</th>
                <th>الجهاز</th>
                <th>الحالة</th>
                <th>ملاحظة</th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($devices as $i => $device) {
                echo "<tr>";
                echo "<td>" . ($i + 1) . "</td>";
                echo "<td>" . htmlspecialchars($device) . "</td>";
                echo "<td>" . htmlspecialchars($statuses[$device] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($notes[$device] ?? '-') . "</td>";
                echo "</tr>";
            }
            ?>
        </tbody>
    </table>
    <div style="margin-top: 25px;">
        <a href="index.php" class="btn btn-primary" style="margin-left: 10px;">رجوع للرئيسية</a>
        <?php if ($success) { ?>
            <a href="../dashboard/view_central_status.php" class="btn btn-success">عرض جميع المواقف</a>
        <?php } ?>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
