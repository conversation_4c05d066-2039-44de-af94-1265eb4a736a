<?php
session_start();
if (!isset($_SESSION['central_status_result'])) {
    header('Location: add_status.php');
    exit;
}
$data = $_SESSION['central_status_result'];
$devices = $data['devices'];
$statuses = $data['statuses'];
$notes = $data['notes'];
$time = $data['time'];
unset($_SESSION['central_status_result']);
include '../includes/header.php';
renderHeader("تأكيد الإرسال", true, "index.php");
?>

<style>
    .confirm-section {
        background: #fff;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(30,60,114,0.13);
        padding: 38px 22px 28px 22px;
        margin: 48px auto 0 auto;
        max-width: 850px;
        border: 1.5px solid #1da1f2;
        text-align: center;
    }
    .confirm-section h3 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 18px;
    }
    .confirm-section .time {
        color: #28a745;
        font-size: 1.1em;
        margin-bottom: 18px;
    }
    .confirm-table {
        margin: 0 auto 18px auto;
        width: 100%;
        background: #f7fafd;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(29,161,242,0.07);
    }
    .confirm-table th {
        background: #1da1f2;
        color: #fff;
        font-size: 1.05em;
        font-weight: bold;
    }
    .confirm-table td {
        color: #232526;
        background: #eaf6fb;
        font-size: 1em;
    }
    .confirm-table tr:nth-child(even) td {
        background: #d6eaf8;
    }
</style>

<div class="confirm-section">
    <h3>✅ تم إرسال موقف الاتصالات بنجاح</h3>
    <div class="time">وقت الإرسال: <?= htmlspecialchars($time) ?></div>
    <table class="confirm-table">
        <thead>
            <tr>
                <th>#</th>
                <th>الجهاز</th>
                <th>الحالة</th>
                <th>ملاحظة</th>
            </tr>
        </thead>
        <tbody>
            <?php
            foreach ($devices as $i => $device) {
                echo "<tr>";
                echo "<td>" . ($i + 1) . "</td>";
                echo "<td>" . htmlspecialchars($device) . "</td>";
                echo "<td>" . htmlspecialchars($statuses[$device] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($notes[$device] ?? '-') . "</td>";
                echo "</tr>";
            }
            ?>
        </tbody>
    </table>
    <a href="index.php" class="btn btn-central">رجوع</a>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
