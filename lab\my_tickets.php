<?php
session_start();
include '../includes/auth_lab.php';
include '../config/db.php';

// جلب التذاكر الخاصة بالمستخدم
$tickets = [];
$sender_name = $_SESSION['user']['ar_name'] ?? 'مستخدم المخابر';

// التحقق من وجود جدول التذاكر
$table_check = $conn->query("SHOW TABLES LIKE 'tickets'");
if ($table_check && $table_check->num_rows > 0) {
    $stmt = $conn->prepare("SELECT * FROM tickets WHERE sender_department = 'المخابر' AND sender_name = ? ORDER BY created_at DESC");
    $stmt->bind_param("s", $sender_name);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $tickets[] = $row;
    }
    $stmt->close();
}

include '../includes/header.php';
renderHeader("متابعة التذاكر - المخابر", true, "index.php");
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .tickets-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .ticket-card {
        background: #1E2124;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        border-left: 4px solid #1DA1F2;
        transition: all 0.3s ease;
    }
    .ticket-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.2);
    }
    .ticket-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }
    .ticket-title {
        color: #1DA1F2;
        font-weight: bold;
        font-size: 1.2em;
        margin: 0;
    }
    .ticket-id {
        background: #2a2d31;
        color: #1DA1F2;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: bold;
    }
    .ticket-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }
    .meta-item {
        background: #2a2d31;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        color: #aaa;
    }
    .status-open { background: #ffc107; color: #000; }
    .status-processing { background: #17a2b8; color: #fff; }
    .status-closed { background: #28a745; color: #fff; }
    .priority-عاجل { background: #dc3545; color: #fff; }
    .priority-عالي { background: #fd7e14; color: #fff; }
    .priority-متوسط { background: #ffc107; color: #000; }
    .priority-منخفض { background: #28a745; color: #fff; }
    .ticket-description {
        color: #ccc;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    .ticket-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    .btn-view {
        background: #17a2b8;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9em;
        transition: all 0.3s ease;
    }
    .btn-view:hover {
        background: #138496;
        transform: translateY(-1px);
    }
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    .empty-state i {
        font-size: 4em;
        margin-bottom: 20px;
        color: #444;
    }
</style>

<div class="tickets-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 style="color: #1DA1F2;">📊 متابعة التذاكر</h2>
        <a href="send_ticket.php" class="btn btn-outline-primary">
            <i class="fas fa-plus"></i> تذكرة جديدة
        </a>
    </div>

    <?php if (empty($tickets)): ?>
        <div class="empty-state">
            <i class="fas fa-inbox"></i>
            <h4 style="color: #666;">لا توجد تذاكر</h4>
            <p>لم تقم بإرسال أي تذاكر بعد</p>
            <a href="send_ticket.php" class="btn btn-primary mt-3">
                <i class="fas fa-paper-plane"></i> إرسال أول تذكرة
            </a>
        </div>
    <?php else: ?>
        <?php foreach ($tickets as $ticket): ?>
            <div class="ticket-card">
                <div class="ticket-header">
                    <h5 class="ticket-title"><?= htmlspecialchars($ticket['title']) ?></h5>
                    <span class="ticket-id">#<?= $ticket['id'] ?></span>
                </div>
                
                <div class="ticket-meta">
                    <span class="meta-item status-<?= $ticket['status'] === 'مفتوحة' ? 'open' : ($ticket['status'] === 'قيد المعالجة' ? 'processing' : 'closed') ?>">
                        <?php
                        $status_icons = [
                            'مفتوحة' => '🟡',
                            'قيد المعالجة' => '🔵', 
                            'مغلقة' => '🟢'
                        ];
                        echo $status_icons[$ticket['status']] ?? '⚪';
                        ?> <?= $ticket['status'] ?>
                    </span>
                    <span class="meta-item priority-<?= $ticket['priority'] ?>">
                        <?php
                        $priority_icons = [
                            'عاجل' => '🔴',
                            'عالي' => '🟠',
                            'متوسط' => '🟡',
                            'منخفض' => '🟢'
                        ];
                        echo $priority_icons[$ticket['priority']] ?? '⚪';
                        ?> <?= $ticket['priority'] ?>
                    </span>
                    <span class="meta-item">📂 <?= $ticket['category'] ?></span>
                    <span class="meta-item">📅 <?= date('Y/m/d - h:i A', strtotime($ticket['created_at'])) ?></span>
                </div>
                
                <div class="ticket-description">
                    <?= nl2br(htmlspecialchars(substr($ticket['description'], 0, 200))) ?>
                    <?= strlen($ticket['description']) > 200 ? '...' : '' ?>
                </div>
                
                <div class="ticket-actions">
                    <?php if ($ticket['attachment_path']): ?>
                        <a href="<?= htmlspecialchars($ticket['attachment_path']) ?>" target="_blank" class="btn btn-view">
                            <i class="fas fa-paperclip"></i> المرفق
                        </a>
                    <?php endif; ?>
                    <button class="btn btn-view" onclick="viewTicketDetails(<?= $ticket['id'] ?>)">
                        <i class="fas fa-eye"></i> التفاصيل
                    </button>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="text-center mt-4">
            <p style="color: #666;">
                إجمالي التذاكر: <strong><?= count($tickets) ?></strong>
            </p>
        </div>
    <?php endif; ?>
</div>

<script>
function viewTicketDetails(ticketId) {
    // يمكن تطوير هذه الوظيفة لاحقاً لعرض تفاصيل أكثر
    alert('عرض تفاصيل التذكرة #' + ticketId + '\nهذه الميزة قيد التطوير');
}
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
