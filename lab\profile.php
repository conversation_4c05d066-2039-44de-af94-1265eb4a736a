<?php
session_start();
include '../includes/auth_lab.php';
include '../config/db.php';

$success_message = '';
$error_message = '';
$user = $_SESSION['user'];

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $ar_name = trim($_POST['ar_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        
        if (!empty($ar_name)) {
            try {
                $stmt = $conn->prepare("UPDATE users SET ar_name = ?, email = ?, phone = ? WHERE id = ?");
                $stmt->bind_param("sssi", $ar_name, $email, $phone, $user['id']);
                
                if ($stmt->execute()) {
                    $_SESSION['user']['ar_name'] = $ar_name;
                    $success_message = 'تم تحديث الملف الشخصي بنجاح';
                } else {
                    $error_message = 'خطأ في تحديث البيانات';
                }
                $stmt->close();
            } catch (Exception $e) {
                $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
        } else {
            $error_message = 'الاسم مطلوب';
        }
    }
    
    if (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = 'جميع حقول كلمة المرور مطلوبة';
        } elseif ($new_password !== $confirm_password) {
            $error_message = 'كلمة المرور الجديدة غير متطابقة';
        } elseif (strlen($new_password) < 6) {
            $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else {
            try {
                // التحقق من كلمة المرور الحالية
                $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                $result = $stmt->get_result();
                $user_data = $result->fetch_assoc();
                
                if (password_verify($current_password, $user_data['password'])) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt2 = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt2->bind_param("si", $hashed_password, $user['id']);
                    
                    if ($stmt2->execute()) {
                        $success_message = 'تم تغيير كلمة المرور بنجاح';
                    } else {
                        $error_message = 'خطأ في تحديث كلمة المرور';
                    }
                    $stmt2->close();
                } else {
                    $error_message = 'كلمة المرور الحالية غير صحيحة';
                }
                $stmt->close();
            } catch (Exception $e) {
                $error_message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
        }
    }
}

// جلب بيانات المستخدم المحدثة
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param("i", $user['id']);
$stmt->execute();
$result = $stmt->get_result();
$user_data = $result->fetch_assoc();
$stmt->close();

include '../includes/header.php';
renderHeader("الملف الشخصي - المخابر", true, "index.php");
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    .profile-card {
        background: #1E2124;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }
    .form-control {
        background: #2a2d31 !important;
        border: 1px solid #444;
        color: #fff !important;
        border-radius: 10px;
    }
    .form-control:focus {
        background: #2a2d31 !important;
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
        color: #fff !important;
    }
    .form-label {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .btn-update {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .btn-update:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(29, 161, 242, 0.4);
    }
    .profile-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #333;
    }
    .profile-avatar {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 2em;
        color: white;
    }
    .section-title {
        color: #1DA1F2;
        border-bottom: 2px solid #1DA1F2;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>

<div class="profile-container">
    <?php if ($success_message): ?>
        <div class="alert alert-success text-center">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger text-center">
            <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
        </div>
    <?php endif; ?>

    <div class="profile-card">
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <h4 style="color: #1DA1F2;"><?= htmlspecialchars($user_data['ar_name']) ?></h4>
            <p style="color: #aaa;">قسم المخابر</p>
        </div>

        <!-- تحديث المعلومات الشخصية -->
        <h5 class="section-title">📝 المعلومات الشخصية</h5>
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="ar_name" class="form-label">الاسم بالعربية</label>
                        <input type="text" class="form-control" id="ar_name" name="ar_name" 
                               value="<?= htmlspecialchars($user_data['ar_name']) ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" value="<?= htmlspecialchars($user_data['username']) ?>" disabled>
                        <small class="text-muted">لا يمكن تغيير اسم المستخدم</small>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= htmlspecialchars($user_data['email'] ?? '') ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="<?= htmlspecialchars($user_data['phone'] ?? '') ?>">
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button type="submit" name="update_profile" class="btn btn-update">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>

    <!-- تغيير كلمة المرور -->
    <div class="profile-card">
        <h5 class="section-title">🔒 تغيير كلمة المرور</h5>
        <form method="POST">
            <div class="mb-3">
                <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                <input type="password" class="form-control" id="current_password" name="current_password" required>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" 
                               minlength="6" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               minlength="6" required>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button type="submit" name="change_password" class="btn btn-update">
                    <i class="fas fa-key"></i> تغيير كلمة المرور
                </button>
            </div>
        </form>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
