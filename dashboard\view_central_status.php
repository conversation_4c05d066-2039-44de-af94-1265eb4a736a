<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("عرض مواقف اتصالات المركزية", false, "index.php");

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    include '../config/db.php';

    $action = $_POST['action'] ?? '';

    if ($action === 'delete_reports') {
        $reports_json = $_POST['reports'] ?? '[]';
        $reports = json_decode($reports_json, true);
        $deleted_count = 0;

        if (!is_array($reports)) {
            echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
            exit;
        }

        try {
            $conn->begin_transaction();

            foreach ($reports as $report_time) {
                $stmt = $conn->prepare("DELETE FROM central_status WHERE sent_at = ?");
                $stmt->bind_param("s", $report_time);
                if ($stmt->execute()) {
                    $deleted_count += $stmt->affected_rows;
                }
                $stmt->close();
            }

            $conn->commit();
            echo json_encode(['success' => true, 'deleted_count' => $deleted_count, 'message' => "تم حذف $deleted_count سجل بنجاح"]);
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
        }

        $conn->close();
        exit;
    }

    if ($action === 'save_edited_reports') {
        $reports = json_decode($_POST['reports'], true);
        $updated_count = 0;

        try {
            $conn->begin_transaction();

            foreach ($reports as $report) {
                foreach ($report['devices'] as $device) {
                    $stmt = $conn->prepare("UPDATE central_status SET status = ?, note = ? WHERE sent_at = ? AND device = ?");
                    $stmt->bind_param("ssss", $device['status'], $device['note'], $report['time'], $device['device']);
                    if ($stmt->execute()) {
                        $updated_count += $stmt->affected_rows;
                    }
                    $stmt->close();
                }
            }

            $conn->commit();
            echo json_encode(['success' => true, 'updated_count' => $updated_count]);
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }

        $conn->close();
        exit;
    }

    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    exit;
}

// جلب المواقف من قاعدة البيانات خلال آخر 24 ساعة
$statuses = [];
$total_count = 0;
include '../config/db.php';

// حساب الوقت قبل 24 ساعة
$twenty_four_hours_ago = date('Y-m-d H:i:s', strtotime('-24 hours'));

$res = $conn->query("SELECT sent_at, device, status, note FROM central_status
                     WHERE sent_at >= '$twenty_four_hours_ago'
                     ORDER BY sent_at DESC, id ASC");
if ($res) {
    while ($row = $res->fetch_assoc()) {
        $statuses[$row['sent_at']][] = $row;
        $total_count++;
    }
}
$conn->close();
?>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', Tahoma, Arial, sans-serif;
    }
    .central-status-section {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        padding: 30px;
        margin: 30px auto;
        max-width: 1200px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    .page-title {
        text-align: center;
        color: #2c3e50;
        font-size: 2.5em;
        font-weight: 800;
        margin-bottom: 30px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .controls-bar {
        background: white;
        border-radius: 15px;
        padding: 20px 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    .selection-controls {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }
    .btn-control {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9em;
    }
    .btn-control:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102,126,234,0.3);
    }
    .print-controls {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    .btn-edit {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 10px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        box-shadow: 0 4px 15px rgba(255,193,7,0.2);
    }
    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255,193,7,0.3);
    }
    .btn-print {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 10px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        box-shadow: 0 4px 15px rgba(40,167,69,0.2);
    }
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40,167,69,0.3);
    }
    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        box-shadow: 0 4px 15px rgba(220,53,69,0.2);
    }
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220,53,69,0.3);
    }
    .selected-count {
        color: #667eea;
        font-weight: 600;
        font-size: 0.95em;
    }
    .reports-container {
        display: grid;
        gap: 25px;
    }
    .report-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    .report-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        border-color: #667eea;
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }
    .report-info {
        flex: 1;
    }
    .header-controls {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    .checkbox-container {
        position: relative;
        cursor: pointer;
        font-size: 18px;
        user-select: none;
    }
    .checkbox-container input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }
    .checkmark {
        position: relative;
        display: inline-block;
        height: 20px;
        width: 20px;
        background-color: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.5);
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    .checkbox-container:hover .checkmark {
        background-color: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.8);
    }
    .checkbox-container input:checked ~ .checkmark {
        background-color: #28a745;
        border-color: #28a745;
    }
    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    .checkbox-container input:checked ~ .checkmark:after {
        display: block;
    }
    .report-time {
        font-size: 1.1em;
        font-weight: 600;
    }
    .report-date {
        font-size: 0.9em;
        opacity: 0.9;
    }
    .devices-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
        padding: 25px;
    }
    .device-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .device-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    .device-item.success {
        border-left-color: #28a745;
    }
    .device-item.danger {
        border-left-color: #dc3545;
    }
    .device-name {
        font-weight: 700;
        font-size: 1.1em;
        color: #2c3e50;
    }
    .device-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
    }
    .device-status.success {
        color: #28a745;
    }
    .device-status.danger {
        color: #dc3545;
    }
    .device-note {
        font-size: 0.85em;
        color: #6c757d;
        margin-top: 5px;
        font-style: italic;
    }
    .no-data {
        text-align: center;
        padding: 80px 20px;
        color: #6c757d;
    }
    .no-data-icon {
        font-size: 4em;
        margin-bottom: 20px;
        color: #667eea;
    }
    .no-data-text {
        font-size: 1.3em;
        margin-bottom: 10px;
    }
    .expand-icon {
        transition: transform 0.3s ease;
        font-size: 1.2em;
        color: rgba(255,255,255,0.8);
    }
    .report-card.expanded .expand-icon {
        transform: rotate(180deg);
    }
    .report-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    .report-card.expanded .report-content {
        max-height: 2000px;
    }
    .last-update {
        text-align: center;
        margin-top: 30px;
        color: #6c757d;
        font-size: 0.9em;
        padding: 15px;
        background: rgba(108,117,125,0.1);
        border-radius: 10px;
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .central-status-section {
            margin: 15px;
            padding: 20px;
        }
        .page-title {
            font-size: 2em;
        }
        .controls-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 15px;
        }
        .selection-controls {
            justify-content: center;
        }
        .print-controls {
            flex-direction: column;
            gap: 10px;
        }
        .btn-edit, .btn-delete, .btn-print {
            width: 100%;
            text-align: center;
        }
        .edit-modal-content {
            width: 98%;
            margin: 1% auto;
        }
        .edit-modal-header {
            padding: 15px 20px;
        }
        .edit-modal-title {
            font-size: 1.2em;
        }
        .edit-modal-body {
            padding: 20px;
        }
        .edit-device-item {
            grid-template-columns: 1fr;
            gap: 10px;
            text-align: center;
        }
        .edit-status-group {
            justify-content: center;
        }
        .edit-modal-footer {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
        .edit-actions {
            width: 100%;
            justify-content: center;
        }
        .devices-grid {
            grid-template-columns: 1fr;
            padding: 20px;
        }
        .report-header {
            padding: 15px 20px;
        }
        .report-info {
            flex: 1;
        }
        .header-controls {
            gap: 10px;
        }
        .device-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        .device-status {
            align-self: flex-end;
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }
        .page-title {
            font-size: 1.6em;
        }
        .stat-number {
            font-size: 1.8em;
        }
        .report-time {
            font-size: 1em;
        }
        .device-name {
            font-size: 1em;
        }
    }

    /* تحسينات إضافية */
    @supports not (backdrop-filter: blur(10px)) {
        .central-status-section {
            background: rgba(255,255,255,0.98);
        }
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .report-card {
        animation: fadeInUp 0.6s ease forwards;
    }

    /* نافذة التعديل */
    .edit-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
    }
    .edit-modal-content {
        background-color: white;
        margin: 2% auto;
        padding: 0;
        border-radius: 15px;
        width: 95%;
        max-width: 1200px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }
    .edit-modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .edit-modal-title {
        font-size: 1.5em;
        font-weight: 700;
        margin: 0;
    }
    .close-modal {
        background: none;
        border: none;
        color: white;
        font-size: 2em;
        cursor: pointer;
        padding: 0;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    .close-modal:hover {
        background: rgba(255,255,255,0.2);
        transform: rotate(90deg);
    }
    .edit-modal-body {
        padding: 30px;
    }
    .edit-reports-grid {
        display: grid;
        gap: 25px;
    }
    .edit-report-card {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        background: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .edit-report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: 600;
        font-size: 1.1em;
    }
    .edit-devices-container {
        padding: 20px;
    }
    .edit-device-item {
        display: grid;
        grid-template-columns: 1fr 2fr 3fr;
        gap: 15px;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #f1f3f4;
        transition: background 0.3s ease;
    }
    .edit-device-item:hover {
        background: #f8f9fa;
    }
    .edit-device-item:last-child {
        border-bottom: none;
    }
    .edit-device-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1.1em;
    }
    .edit-status-group {
        display: flex;
        gap: 15px;
    }
    .edit-status-option {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    .edit-status-option:hover {
        background: #f8f9fa;
    }
    .edit-status-option input[type="radio"] {
        margin: 0;
        transform: scale(1.2);
    }
    .edit-status-option.success {
        color: #28a745;
        font-weight: 600;
    }
    .edit-status-option.danger {
        color: #dc3545;
        font-weight: 600;
    }
    .edit-note-input {
        width: 100%;
        padding: 10px 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.95em;
        transition: all 0.3s ease;
        resize: vertical;
        min-height: 40px;
    }
    .edit-note-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
    }
    .edit-modal-footer {
        padding: 20px 30px;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
        border-radius: 0 0 15px 15px;
    }
    .edit-actions {
        display: flex;
        gap: 15px;
    }
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40,167,69,0.3);
    }
    .btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108,117,125,0.3);
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .central-status-section {
            margin: 20px 15px 0 15px;
            padding: 25px 15px 20px 15px;
            max-width: none;
        }
        .central-status-section h3 {
            font-size: 1.6em;
        }
        .stats-bar {
            flex-direction: column;
            gap: 15px;
            padding: 20px 15px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .stats-item:last-child {
            border-bottom: none;
        }
        .time-header {
            flex-direction: column;
            gap: 5px;
            text-align: center;
            padding: 15px;
        }
        .central-table {
            font-size: 0.9em;
        }
        .central-table th,
        .central-table td {
            padding: 12px 8px;
        }
    }

    @media (max-width: 480px) {
        .central-table {
            font-size: 0.8em;
        }
        .central-table th,
        .central-table td {
            padding: 10px 6px;
        }
        .stats-number {
            font-size: 1.4em;
        }
        .device-name {
            font-size: 1em;
        }
    }

    /* تحسينات إضافية */
    @supports not (backdrop-filter: blur(10px)) {
        .central-status-section {
            background: rgba(255,255,255,0.98);
        }
    }
</style>

<div class="central-status-section">
    <h1 class="page-title">📋 مواقف اتصالات المركزية</h1>

    <?php if (empty($statuses)) { ?>
        <div class="no-data">
            <div class="no-data-icon">📭</div>
            <div class="no-data-text">لا توجد مواقف مسجلة خلال آخر 24 ساعة</div>
            <small>آخر تحديث: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    <?php } else { ?>
        <!-- أدوات التحكم -->
        <div class="controls-bar">
            <div class="selection-controls">
                <button type="button" class="btn-control" onclick="selectAll()">تحديد الكل</button>
                <button type="button" class="btn-control" onclick="deselectAll()">إلغاء التحديد</button>
                <span class="selected-count">المحدد: <span id="selectedCount">0</span></span>
            </div>
            <div class="print-controls">
                <button type="button" class="btn-edit" onclick="editSelected()">
                    ✏️ تعديل المحدد
                </button>
                <button type="button" class="btn-delete" onclick="deleteSelected()">
                    🗑️ حذف المحدد
                </button>
                <button type="button" class="btn-print" onclick="printSelected()">
                    🖨️ طباعة المحدد
                </button>
            </div>
        </div>

        <!-- التقارير -->
        <div class="reports-container">
            <?php foreach ($statuses as $sent_at => $rows) { ?>
                <div class="report-card" data-time="<?= $sent_at ?>">
                    <div class="report-header" onclick="toggleReport(this.parentElement)">
                        <div class="report-info">
                            <div class="report-time">📅 <?= date('Y-m-d', strtotime($sent_at)) ?></div>
                            <div class="report-date">🕐 <?= date('H:i:s', strtotime($sent_at)) ?></div>
                        </div>
                        <div class="header-controls">
                            <label class="checkbox-container" onclick="event.stopPropagation()">
                                <input type="checkbox" class="report-checkbox" onchange="updateSelectedCount()">
                                <span class="checkmark"></span>
                            </label>
                            <div class="expand-icon">▼</div>
                        </div>
                    </div>

                    <div class="report-content">
                        <div class="devices-grid">
                            <?php foreach ($rows as $row) {
                                $status_class = ($row['status'] == 'تم الرد') ? 'success' : 'danger';
                                $status_icon = ($row['status'] == 'تم الرد') ? '✅' : '❌';
                            ?>
                                <div class="device-item <?= $status_class ?>">
                                    <div>
                                        <div class="device-name"><?= htmlspecialchars($row['device']) ?></div>
                                        <?php if (!empty($row['note'])) { ?>
                                            <div class="device-note"><?= htmlspecialchars($row['note']) ?></div>
                                        <?php } ?>
                                    </div>
                                    <div class="device-status <?= $status_class ?>">
                                        <?= $status_icon ?> <?= htmlspecialchars($row['status']) ?>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>

        <div class="last-update">
            آخر تحديث: <?= date('Y-m-d H:i:s') ?> |
            عرض البيانات من: <?= date('Y-m-d H:i:s', strtotime('-24 hours')) ?>
        </div>
    <?php } ?>
</div>

<!-- نافذة التعديل -->
<div id="editModal" class="edit-modal">
    <div class="edit-modal-content">
        <div class="edit-modal-header">
            <h2 class="edit-modal-title">✏️ تعديل التقارير المحددة</h2>
            <button class="close-modal" onclick="closeEditModal()">&times;</button>
        </div>
        <div class="edit-modal-body">
            <div id="editReportsContainer" class="edit-reports-grid">
                <!-- سيتم إدراج التقارير هنا بواسطة JavaScript -->
            </div>
        </div>
        <div class="edit-modal-footer">
            <div class="edit-info">
                <span>💡 يمكنك تعديل الحالات والملاحظات - سيتم الحفظ فقط بدون طباعة</span>
            </div>
            <div class="edit-actions">
                <button class="btn-cancel" onclick="closeEditModal()">إلغاء</button>
                <button class="btn-save" onclick="saveChangesOnly()">💾 حفظ فقط</button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleReport(element) {
    element.classList.toggle('expanded');
}

// تحديد جميع التقارير
function selectAll() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

// إلغاء تحديد جميع التقارير
function deselectAll() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// تحديث عداد المحدد
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    document.getElementById('selectedCount').textContent = selectedCount;
}

// فتح نافذة التعديل للتقارير المحددة
function editSelected() {
    const selectedReports = [];
    const checkboxes = document.querySelectorAll('.report-checkbox');

    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            const reportCard = checkbox.closest('.report-card');
            selectedReports.push(reportCard);
        }
    });

    if (selectedReports.length === 0) {
        alert('يرجى تحديد تقرير واحد على الأقل للتعديل');
        return;
    }

    // إنشاء محتوى نافذة التعديل
    generateEditModal(selectedReports);

    // إظهار النافذة
    document.getElementById('editModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// إنشاء محتوى نافذة التعديل
function generateEditModal(reports) {
    const container = document.getElementById('editReportsContainer');
    container.innerHTML = '';

    reports.forEach((report, reportIndex) => {
        const timeAttr = report.getAttribute('data-time');
        const reportDate = new Date(timeAttr);
        const gregorianDate = reportDate.toLocaleDateString('ar-SA');
        const gregorianTime = reportDate.toLocaleTimeString('ar-SA');
        const hijriDate = convertToHijri(timeAttr);
        const devices = report.querySelectorAll('.device-item');

        const reportCard = document.createElement('div');
        reportCard.className = 'edit-report-card';
        reportCard.setAttribute('data-time', timeAttr);
        reportCard.setAttribute('data-report-index', reportIndex);

        let reportHTML = `
            <div class="edit-report-header">
                📅 ${gregorianDate} - ${gregorianTime} | 🌙 ${hijriDate.formatted}
            </div>
            <div class="edit-devices-container">
        `;

        devices.forEach((device, deviceIndex) => {
            const deviceName = device.querySelector('.device-name').textContent;
            const deviceStatus = device.querySelector('.device-status').textContent;
            const deviceNote = device.querySelector('.device-note');
            const note = deviceNote ? deviceNote.textContent : '';
            const isSuccess = device.classList.contains('success');

            reportHTML += `
                <div class="edit-device-item">
                    <div class="edit-device-name">${deviceName}</div>
                    <div class="edit-status-group">
                        <label class="edit-status-option success">
                            <input type="radio" name="status_${reportIndex}_${deviceIndex}" value="تم الرد" ${isSuccess ? 'checked' : ''}>
                            <span>✅ تم الرد</span>
                        </label>
                        <label class="edit-status-option danger">
                            <input type="radio" name="status_${reportIndex}_${deviceIndex}" value="لا يوجد رد" ${!isSuccess ? 'checked' : ''}>
                            <span>❌ لا يوجد رد</span>
                        </label>
                    </div>
                    <textarea class="edit-note-input" placeholder="أدخل الملاحظة..." data-device="${deviceName}">${note}</textarea>
                </div>
            `;
        });

        reportHTML += `
            </div>
        `;

        reportCard.innerHTML = reportHTML;
        container.appendChild(reportCard);
    });
}

// إغلاق نافذة التعديل
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// حفظ التغييرات فقط (بدون طباعة)
function saveChangesOnly() {
    const editedReports = [];
    const reportCards = document.querySelectorAll('.edit-report-card');

    reportCards.forEach(reportCard => {
        const timeAttr = reportCard.getAttribute('data-time');
        const reportIndex = reportCard.getAttribute('data-report-index');
        const devices = [];

        const deviceItems = reportCard.querySelectorAll('.edit-device-item');
        deviceItems.forEach((deviceItem, deviceIndex) => {
            const deviceName = deviceItem.querySelector('.edit-device-name').textContent;
            const statusRadios = deviceItem.querySelectorAll(`input[name="status_${reportIndex}_${deviceIndex}"]`);
            const noteInput = deviceItem.querySelector('.edit-note-input');

            let selectedStatus = '';
            statusRadios.forEach(radio => {
                if (radio.checked) {
                    selectedStatus = radio.value;
                }
            });

            devices.push({
                name: deviceName,
                status: selectedStatus,
                note: noteInput.value.trim(),
                isSuccess: selectedStatus === 'تم الرد'
            });
        });

        editedReports.push({
            time: timeAttr,
            devices: devices
        });
    });

    // إرسال البيانات المعدلة للحفظ في قاعدة البيانات
    const formData = new FormData();
    formData.append('action', 'save_edited_reports');
    formData.append('reports', JSON.stringify(editedReports));

    // إظهار مؤشر التحميل
    showNotification('💾 جاري حفظ التعديلات...', 'info');

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        console.log('Server response:', text); // للتشخيص
        try {
            const data = JSON.parse(text);
            if (data.success) {
                showNotification('✅ تم حفظ التعديلات بنجاح', 'success');

                // إعادة تحميل الصفحة بعد ثانيتين
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showNotification('❌ حدث خطأ أثناء الحفظ: ' + (data.message || 'خطأ غير معروف'), 'error');
            }
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response text:', text);
            showNotification('❌ خطأ في معالجة استجابة الخادم', 'error');
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        showNotification('❌ حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
    });

    // إغلاق نافذة التعديل
    closeEditModal();
}

// حذف التقارير المحددة
function deleteSelected() {
    const selectedReports = [];
    const checkboxes = document.querySelectorAll('.report-checkbox');

    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            const reportCard = checkbox.closest('.report-card');
            const reportTime = reportCard.querySelector('.report-time').textContent.trim();
            selectedReports.push({
                time: reportTime,
                element: reportCard
            });
        }
    });

    if (selectedReports.length === 0) {
        alert('يرجى تحديد تقرير واحد على الأقل للحذف');
        return;
    }

    // تأكيد الحذف
    const confirmDelete = confirm(
        `⚠️ تحذير: هل أنت متأكد من حذف ${selectedReports.length} تقرير؟\n\n` +
        'هذا الإجراء لا يمكن التراجع عنه!\n\n' +
        '✅ موافق = حذف نهائي\n' +
        '❌ إلغاء = إلغاء العملية'
    );

    if (!confirmDelete) {
        return;
    }

    // إظهار مؤشر التحميل
    showNotification('🗑️ جاري حذف التقارير المحددة...', 'warning');

    // إرسال طلب الحذف
    const formData = new FormData();
    formData.append('action', 'delete_reports');

    // إرسال التقارير كـ JSON
    const reportTimes = selectedReports.map(report => report.time);
    formData.append('reports', JSON.stringify(reportTimes));

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        console.log('Server response:', text); // للتشخيص
        try {
            const data = JSON.parse(text);
            if (data.success) {
                // إزالة التقارير من الواجهة
                selectedReports.forEach(report => {
                    report.element.style.animation = 'slideOutLeft 0.5s ease';
                    setTimeout(() => {
                        report.element.remove();
                    }, 500);
                });

                // تحديث العداد
                updateSelectedCount();

                // إظهار رسالة نجاح
                showNotification(`✅ ${data.message || 'تم الحذف بنجاح'}`, 'success');

                // إعادة تحميل الصفحة بعد 2 ثانية
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showNotification('❌ حدث خطأ أثناء الحذف: ' + (data.message || 'خطأ غير معروف'), 'error');
            }
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response text:', text);
            showNotification('❌ خطأ في معالجة استجابة الخادم', 'error');
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        showNotification('❌ حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
    });
}

// طباعة التقارير المحددة (بدون تعديل)
function printSelected() {
    const selectedReports = [];
    const checkboxes = document.querySelectorAll('.report-checkbox');

    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            const reportCard = checkbox.closest('.report-card');
            selectedReports.push(reportCard);
        }
    });

    if (selectedReports.length === 0) {
        alert('يرجى تحديد تقرير واحد على الأقل للطباعة');
        return;
    }

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent(selectedReports);

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// تحويل التاريخ من ميلادي إلى هجري
function convertToHijri(gregorianDate) {
    const date = new Date(gregorianDate);

    // حساب تقريبي للتاريخ الهجري
    const gregorianYear = date.getFullYear();
    const gregorianMonth = date.getMonth() + 1;
    const gregorianDay = date.getDate();

    // معادلة تحويل تقريبية
    const hijriYear = Math.floor((gregorianYear - 622) * 1.030684);

    // أسماء الأشهر الهجرية
    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    // تقدير الشهر الهجري (تقريبي)
    const hijriMonth = ((gregorianMonth + 8) % 12);
    const hijriDay = gregorianDay;

    return {
        day: hijriDay,
        month: hijriMonths[hijriMonth],
        year: hijriYear,
        formatted: `${hijriDay} ${hijriMonths[hijriMonth]} ${hijriYear}هـ`
    };
}

// إنشاء محتوى الطباعة المحسن
function generatePrintContent(reports) {
    const reportsPerPage = 4;
    const totalPages = Math.ceil(reports.length / reportsPerPage);

    let content = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير مواقف اتصالات المركزية</title>
        <style>
            @page {
                size: A4 landscape;
                margin: 10mm;
            }
            body {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                margin: 0;
                padding: 0;
                direction: rtl;
                color: #333;
                font-size: 10px;
                line-height: 1.3;
            }
            .page {
                min-height: 100vh;
                page-break-after: always;
                display: flex;
                flex-direction: column;
            }
            .page:last-child {
                page-break-after: avoid;
            }
            .print-header {
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                border-radius: 6px;
                margin-bottom: 15px;
            }
            .print-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .print-subtitle {
                font-size: 12px;
                opacity: 0.9;
                margin-bottom: 3px;
            }
            .print-date {
                font-size: 10px;
                opacity: 0.8;
            }
            .page-info {
                text-align: center;
                font-size: 9px;
                color: #666;
                margin-bottom: 10px;
            }
            .reports-row {
                display: flex;
                gap: 8px;
                flex: 1;
                align-items: stretch;
            }
            .report-card {
                flex: 1;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                overflow: hidden;
                background: white;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                display: flex;
                flex-direction: column;
            }
            .report-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 8px 6px;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
                flex-shrink: 0;
            }
            .report-time {
                margin-bottom: 2px;
                font-size: 9px;
            }
            .report-hijri {
                font-size: 8px;
                opacity: 0.9;
            }
            .devices-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 8px;
                flex: 1;
            }
            .devices-table th {
                background: #f8f9fa;
                font-weight: bold;
                padding: 4px 2px;
                border-bottom: 1px solid #dee2e6;
                font-size: 7px;
                line-height: 1.2;
            }
            .devices-table td {
                padding: 3px 2px;
                text-align: center;
                border-bottom: 1px solid #f1f3f4;
                vertical-align: middle;
                font-size: 7px;
                line-height: 1.2;
            }
            .devices-table tr:nth-child(even) {
                background: #f8f9fa;
            }
            .device-name {
                font-weight: bold;
                color: #2c5aa0;
                font-size: 8px;
            }
            .status-success {
                color: #28a745;
                font-weight: bold;
                font-size: 7px;
            }
            .status-danger {
                color: #dc3545;
                font-weight: bold;
                font-size: 7px;
            }
            .note-cell {
                font-size: 6px;
                color: #666;
                max-width: 60px;
                word-wrap: break-word;
                line-height: 1.1;
            }
            .footer {
                text-align: center;
                margin-top: auto;
                padding-top: 10px;
                border-top: 1px solid #dee2e6;
                font-size: 8px;
                color: #666;
            }

            /* تحسينات إضافية للتخطيط الأفقي */
            .report-card:nth-child(1) { border-right: 2px solid #667eea; }
            .report-card:nth-child(2) { border-right: 2px solid #28a745; }
            .report-card:nth-child(3) { border-right: 2px solid #ffc107; }
            .report-card:nth-child(4) { border-right: 2px solid #dc3545; }

            @media print {
                body {
                    margin: 0;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }
                .page {
                    page-break-after: always;
                    height: 100vh;
                }
                .page:last-child {
                    page-break-after: avoid;
                }
                .reports-row {
                    height: calc(100vh - 120px);
                }
                .report-card {
                    height: 100%;
                }
            }
        </style>
    </head>
    <body>
    `;

    // تقسيم التقارير إلى صفحات
    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
        const startIndex = pageIndex * reportsPerPage;
        const endIndex = Math.min(startIndex + reportsPerPage, reports.length);
        const pageReports = reports.slice(startIndex, endIndex);

        content += `
        <div class="page">
            <div class="print-header">
                <div class="print-title">📋 تقرير مواقف اتصالات المركزية</div>
                <div class="print-subtitle">وزارة الداخلية - قيادة عمليات بغداد</div>
                <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
            </div>

            <div class="page-info">
                صفحة ${pageIndex + 1} من ${totalPages} | التقارير ${startIndex + 1} - ${endIndex} من ${reports.length}
            </div>

            <div class="reports-row">
        `;

        pageReports.forEach(report => {
            const timeAttr = report.getAttribute('data-time');
            const reportDate = new Date(timeAttr);
            const gregorianDate = reportDate.toLocaleDateString('ar-SA');
            const gregorianTime = reportDate.toLocaleTimeString('ar-SA');
            const hijriDate = convertToHijri(timeAttr);
            const devices = report.querySelectorAll('.device-item');

            content += `
            <div class="report-card">
                <div class="report-header">
                    <div class="report-time">📅 ${gregorianDate} - ${gregorianTime}</div>
                    <div class="report-hijri">🌙 ${hijriDate.formatted}</div>
                </div>

                <table class="devices-table">
                    <thead>
                        <tr>
                            <th style="width: 12%">#</th>
                            <th style="width: 30%">الجهاز</th>
                            <th style="width: 25%">الحالة</th>
                            <th style="width: 33%">الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            devices.forEach((device, index) => {
                const deviceName = device.querySelector('.device-name').textContent;
                const deviceStatus = device.querySelector('.device-status').textContent;
                const deviceNote = device.querySelector('.device-note');
                const note = deviceNote ? deviceNote.textContent : '-';
                const statusClass = device.classList.contains('success') ? 'status-success' : 'status-danger';

                content += `
                    <tr>
                        <td>${index + 1}</td>
                        <td class="device-name">${deviceName}</td>
                        <td class="${statusClass}">${deviceStatus}</td>
                        <td class="note-cell">${note}</td>
                    </tr>
                `;
            });

            content += `
                    </tbody>
                </table>
            </div>
            `;
        });

        content += `
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام راصم - جميع الحقوق محفوظة © ${new Date().getFullYear()}
            </div>
        </div>
        `;
    }

    content += `
    </body>
    </html>
    `;

    return content;
}

// إنشاء محتوى الطباعة للبيانات المعدلة
function generateEditedPrintContent(editedReports) {
    const reportsPerPage = 4;
    const totalPages = Math.ceil(editedReports.length / reportsPerPage);

    let content = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير مواقف اتصالات المركزية - معدل</title>
        <style>
            @page {
                size: A4 landscape;
                margin: 10mm;
            }
            body {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                margin: 0;
                padding: 0;
                direction: rtl;
                color: #333;
                font-size: 10px;
                line-height: 1.3;
            }
            .page {
                min-height: 100vh;
                page-break-after: always;
                display: flex;
                flex-direction: column;
            }
            .page:last-child {
                page-break-after: avoid;
            }
            .print-header {
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                border-radius: 6px;
                margin-bottom: 15px;
            }
            .print-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .print-subtitle {
                font-size: 12px;
                opacity: 0.9;
                margin-bottom: 3px;
            }
            .print-date {
                font-size: 10px;
                opacity: 0.8;
            }
            .page-info {
                text-align: center;
                font-size: 9px;
                color: #666;
                margin-bottom: 10px;
            }
            .reports-row {
                display: flex;
                gap: 8px;
                flex: 1;
                align-items: stretch;
            }
            .report-card {
                flex: 1;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                overflow: hidden;
                background: white;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                display: flex;
                flex-direction: column;
            }
            .report-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 8px 6px;
                font-weight: bold;
                font-size: 10px;
                text-align: center;
                flex-shrink: 0;
            }
            .report-time {
                margin-bottom: 2px;
                font-size: 9px;
            }
            .report-hijri {
                font-size: 8px;
                opacity: 0.9;
            }
            .devices-table {
                width: 100%;
                border-collapse: collapse;
                font-size: 8px;
                flex: 1;
            }
            .devices-table th {
                background: #f8f9fa;
                font-weight: bold;
                padding: 4px 2px;
                border-bottom: 1px solid #dee2e6;
                font-size: 7px;
                line-height: 1.2;
            }
            .devices-table td {
                padding: 3px 2px;
                text-align: center;
                border-bottom: 1px solid #f1f3f4;
                vertical-align: middle;
                font-size: 7px;
                line-height: 1.2;
            }
            .devices-table tr:nth-child(even) {
                background: #f8f9fa;
            }
            .device-name {
                font-weight: bold;
                color: #2c5aa0;
                font-size: 8px;
            }
            .status-success {
                color: #28a745;
                font-weight: bold;
                font-size: 7px;
            }
            .status-danger {
                color: #dc3545;
                font-weight: bold;
                font-size: 7px;
            }
            .note-cell {
                font-size: 6px;
                color: #666;
                max-width: 60px;
                word-wrap: break-word;
                line-height: 1.1;
            }
            .footer {
                text-align: center;
                margin-top: auto;
                padding-top: 10px;
                border-top: 1px solid #dee2e6;
                font-size: 8px;
                color: #666;
            }
            .edited-badge {
                background: #ffc107;
                color: #212529;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 7px;
                font-weight: bold;
                margin-left: 5px;
            }

            /* تحسينات إضافية للتخطيط الأفقي */
            .report-card:nth-child(1) { border-right: 2px solid #667eea; }
            .report-card:nth-child(2) { border-right: 2px solid #28a745; }
            .report-card:nth-child(3) { border-right: 2px solid #ffc107; }
            .report-card:nth-child(4) { border-right: 2px solid #dc3545; }

            @media print {
                body {
                    margin: 0;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                }
                .page {
                    page-break-after: always;
                    height: 100vh;
                }
                .page:last-child {
                    page-break-after: avoid;
                }
                .reports-row {
                    height: calc(100vh - 120px);
                }
                .report-card {
                    height: 100%;
                }
            }
        </style>
    </head>
    <body>
    `;

    // تقسيم التقارير إلى صفحات
    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
        const startIndex = pageIndex * reportsPerPage;
        const endIndex = Math.min(startIndex + reportsPerPage, editedReports.length);
        const pageReports = editedReports.slice(startIndex, endIndex);

        content += `
        <div class="page">
            <div class="print-header">
                <div class="print-title">📋 تقرير مواقف اتصالات المركزية <span class="edited-badge">معدل</span></div>
                <div class="print-subtitle">وزارة الداخلية - قيادة عمليات بغداد</div>
                <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</div>
            </div>

            <div class="page-info">
                صفحة ${pageIndex + 1} من ${totalPages} | التقارير ${startIndex + 1} - ${endIndex} من ${editedReports.length}
            </div>

            <div class="reports-row">
        `;

        pageReports.forEach(report => {
            const reportDate = new Date(report.time);
            const gregorianDate = reportDate.toLocaleDateString('ar-SA');
            const gregorianTime = reportDate.toLocaleTimeString('ar-SA');
            const hijriDate = convertToHijri(report.time);

            content += `
            <div class="report-card">
                <div class="report-header">
                    <div class="report-time">📅 ${gregorianDate} - ${gregorianTime}</div>
                    <div class="report-hijri">🌙 ${hijriDate.formatted}</div>
                </div>

                <table class="devices-table">
                    <thead>
                        <tr>
                            <th style="width: 12%">#</th>
                            <th style="width: 30%">الجهاز</th>
                            <th style="width: 25%">الحالة</th>
                            <th style="width: 33%">الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            report.devices.forEach((device, index) => {
                const statusClass = device.isSuccess ? 'status-success' : 'status-danger';
                const statusIcon = device.isSuccess ? '✅' : '❌';
                const note = device.note || '-';

                content += `
                    <tr>
                        <td>${index + 1}</td>
                        <td class="device-name">${device.name}</td>
                        <td class="${statusClass}">${statusIcon} ${device.status}</td>
                        <td class="note-cell">${note}</td>
                    </tr>
                `;
            });

            content += `
                    </tbody>
                </table>
            </div>
            `;
        });

        content += `
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام راصم - تم التعديل قبل الطباعة © ${new Date().getFullYear()}
            </div>
        </div>
        `;
    }

    content += `
    </body>
    </html>
    `;

    return content;
}

// تحديث الصفحة كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);

// تأثيرات الظهور
document.addEventListener('DOMContentLoaded', function() {
    const reportCards = document.querySelectorAll('.report-card');
    reportCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // تحديث العداد في البداية
    updateSelectedCount();
});

// دالة إظهار الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');

    // تحديد الألوان والأيقونات حسب النوع
    let bgColor, textColor, icon;
    switch(type) {
        case 'success':
            bgColor = '#28a745';
            textColor = 'white';
            icon = '✅';
            break;
        case 'warning':
            bgColor = '#ffc107';
            textColor = '#212529';
            icon = '⚠️';
            break;
        case 'error':
            bgColor = '#dc3545';
            textColor = 'white';
            icon = '❌';
            break;
        case 'info':
            bgColor = '#17a2b8';
            textColor = 'white';
            icon = '💡';
            break;
        default:
            bgColor = '#6c757d';
            textColor = 'white';
            icon = 'ℹ️';
    }

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: ${textColor};
        padding: 15px 20px;
        border-radius: 12px;
        z-index: 10000;
        font-weight: 600;
        font-size: 14px;
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        animation: slideInRight 0.4s ease;
        max-width: 350px;
        word-wrap: break-word;
        border: 2px solid rgba(255,255,255,0.2);
    `;
    notification.innerHTML = `${icon} ${message}`;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.4s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 400);
    }, 4000);
}

// إضافة أنيميشن CSS للإشعارات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
