<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("عرض مواقف اتصالات المركزية", false, "index.php");

// جلب المواقف من قاعدة البيانات خلال آخر 24 ساعة
$statuses = [];
$total_count = 0;
include '../config/db.php';

// حساب الوقت قبل 24 ساعة
$twenty_four_hours_ago = date('Y-m-d H:i:s', strtotime('-24 hours'));

$res = $conn->query("SELECT sent_at, device, status, note FROM central_status
                     WHERE sent_at >= '$twenty_four_hours_ago'
                     ORDER BY sent_at DESC, id ASC");
if ($res) {
    while ($row = $res->fetch_assoc()) {
        $statuses[$row['sent_at']][] = $row;
        $total_count++;
    }
}
$conn->close();
?>

<style>
    body {
        background: linear-gradient(135deg, #232526 0%, #1e3c72 100%);
        min-height: 100vh;
        font-family: 'Cairo', Tahoma, Arial, sans-serif;
    }
    .central-status-section {
        background: rgba(255,255,255,0.95);
        border-radius: 22px;
        box-shadow: 0 12px 40px 0 rgba(30,60,114,0.15);
        padding: 40px 30px 35px 30px;
        margin: 48px auto 0 auto;
        max-width: 1100px;
        border: 2px solid rgba(29,161,242,0.3);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }
    .central-status-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1da1f2 0%, #28a745 50%, #1da1f2 100%);
    }
    .central-status-section h3 {
        color: #1DA1F2;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 2.2em;
        text-shadow: 0 2px 4px rgba(29,161,242,0.1);
    }
    .stats-bar {
        background: linear-gradient(135deg, #1da1f2 0%, #28a745 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 4px 15px rgba(29,161,242,0.2);
    }
    .stats-item {
        text-align: center;
    }
    .stats-number {
        font-size: 1.8em;
        font-weight: 700;
        display: block;
    }
    .stats-label {
        font-size: 0.9em;
        opacity: 0.9;
    }
    .time-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        margin: 25px 0 15px 0;
        font-weight: 700;
        font-size: 1.1em;
        box-shadow: 0 4px 15px rgba(40,167,69,0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .central-table {
        margin: 0 auto 25px auto;
        width: 100%;
        background: #ffffff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 6px 20px rgba(29,161,242,0.1);
        border: 1px solid rgba(29,161,242,0.1);
    }
    .central-table th {
        background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
        color: #ffffff;
        font-size: 1.1em;
        font-weight: 700;
        padding: 18px 15px;
        text-align: center;
        border: none;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    .central-table td {
        color: #2c3e50;
        background: #ffffff;
        font-size: 1.05em;
        font-weight: 500;
        padding: 16px 15px;
        text-align: center;
        border-bottom: 1px solid rgba(29,161,242,0.1);
        transition: all 0.3s ease;
    }
    .central-table tr:nth-child(even) td {
        background: rgba(29,161,242,0.05);
    }
    .central-table tbody tr:hover td {
        background: rgba(29,161,242,0.1);
        transform: translateY(-1px);
    }
    .device-name {
        font-weight: 700;
        color: #1da1f2;
        font-size: 1.1em;
    }
    .status-success {
        color: #28a745;
        font-weight: 700;
    }
    .status-danger {
        color: #dc3545;
        font-weight: 700;
    }
    .row-number {
        font-weight: 700;
        color: #6c757d;
    }
    .no-data {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
        font-size: 1.2em;
    }
    .no-data i {
        font-size: 3em;
        margin-bottom: 20px;
        color: #1da1f2;
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .central-status-section {
            margin: 20px 15px 0 15px;
            padding: 25px 15px 20px 15px;
            max-width: none;
        }
        .central-status-section h3 {
            font-size: 1.6em;
        }
        .stats-bar {
            flex-direction: column;
            gap: 15px;
            padding: 20px 15px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .stats-item:last-child {
            border-bottom: none;
        }
        .time-header {
            flex-direction: column;
            gap: 5px;
            text-align: center;
            padding: 15px;
        }
        .central-table {
            font-size: 0.9em;
        }
        .central-table th,
        .central-table td {
            padding: 12px 8px;
        }
    }

    @media (max-width: 480px) {
        .central-table {
            font-size: 0.8em;
        }
        .central-table th,
        .central-table td {
            padding: 10px 6px;
        }
        .stats-number {
            font-size: 1.4em;
        }
        .device-name {
            font-size: 1em;
        }
    }

    /* تحسينات إضافية */
    @supports not (backdrop-filter: blur(10px)) {
        .central-status-section {
            background: rgba(255,255,255,0.98);
        }
    }
</style>

<div class="central-status-section">
    <h3>📋 مواقف اتصالات المركزية - آخر 24 ساعة</h3>

    <?php if (empty($statuses)) { ?>
        <div class="no-data">
            <i class="fas fa-inbox"></i>
            <div>لا توجد مواقف مسجلة خلال آخر 24 ساعة</div>
            <small style="color: #999; margin-top: 10px; display: block;">
                آخر تحديث: <?= date('Y-m-d H:i:s') ?>
            </small>
        </div>
    <?php } else {
        // حساب الإحصائيات
        $responded_count = 0;
        $no_response_count = 0;
        $total_reports = count($statuses);

        foreach ($statuses as $rows) {
            foreach ($rows as $row) {
                if ($row['status'] == 'تم الرد') {
                    $responded_count++;
                } else {
                    $no_response_count++;
                }
            }
        }
    ?>
        <!-- شريط الإحصائيات -->
        <div class="stats-bar">
            <div class="stats-item">
                <span class="stats-number"><?= $total_reports ?></span>
                <span class="stats-label">إجمالي التقارير</span>
            </div>
            <div class="stats-item">
                <span class="stats-number"><?= $total_count ?></span>
                <span class="stats-label">إجمالي الأجهزة</span>
            </div>
            <div class="stats-item">
                <span class="stats-number"><?= $responded_count ?></span>
                <span class="stats-label">تم الرد</span>
            </div>
            <div class="stats-item">
                <span class="stats-number"><?= $no_response_count ?></span>
                <span class="stats-label">لا يوجد رد</span>
            </div>
        </div>

        <?php foreach ($statuses as $sent_at => $rows) { ?>
            <div class="time-header">
                <span>📅 وقت الإرسال: <?= date('Y-m-d', strtotime($sent_at)) ?></span>
                <span>🕐 الساعة: <?= date('H:i:s', strtotime($sent_at)) ?></span>
            </div>

            <table class="central-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الجهاز</th>
                        <th>حالة الاتصال</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    foreach ($rows as $i => $row) {
                        echo "<tr>";
                        echo "<td class='row-number'>" . ($i + 1) . "</td>";
                        echo "<td class='device-name'>" . htmlspecialchars($row['device']) . "</td>";

                        $status_class = ($row['status'] == 'تم الرد') ? 'status-success' : 'status-danger';
                        $status_icon = ($row['status'] == 'تم الرد') ? '✅' : '❌';
                        echo "<td class='$status_class'>$status_icon " . htmlspecialchars($row['status']) . "</td>";

                        $note = !empty($row['note']) ? htmlspecialchars($row['note']) : '<span style="color: #999;">لا توجد ملاحظة</span>';
                        echo "<td>$note</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        <?php } ?>

        <div style="text-align: center; margin-top: 30px; color: #6c757d; font-size: 0.9em;">
            آخر تحديث: <?= date('Y-m-d H:i:s') ?> |
            عرض البيانات من: <?= date('Y-m-d H:i:s', strtotime('-24 hours')) ?>
        </div>
    <?php } ?>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
