<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("عرض مواقف اتصالات المركزية", false, "index.php");

// جلب المواقف من قاعدة البيانات خلال آخر 24 ساعة
$statuses = [];
$total_count = 0;
include '../config/db.php';

// حساب الوقت قبل 24 ساعة
$twenty_four_hours_ago = date('Y-m-d H:i:s', strtotime('-24 hours'));

$res = $conn->query("SELECT sent_at, device, status, note FROM central_status
                     WHERE sent_at >= '$twenty_four_hours_ago'
                     ORDER BY sent_at DESC, id ASC");
if ($res) {
    while ($row = $res->fetch_assoc()) {
        $statuses[$row['sent_at']][] = $row;
        $total_count++;
    }
}
$conn->close();
?>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', Tahoma, Arial, sans-serif;
    }
    .central-status-section {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        padding: 30px;
        margin: 30px auto;
        max-width: 1200px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    .page-title {
        text-align: center;
        color: #2c3e50;
        font-size: 2.5em;
        font-weight: 800;
        margin-bottom: 30px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .controls-bar {
        background: white;
        border-radius: 15px;
        padding: 20px 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    .selection-controls {
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }
    .btn-control {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9em;
    }
    .btn-control:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102,126,234,0.3);
    }
    .btn-print {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 10px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        box-shadow: 0 4px 15px rgba(40,167,69,0.2);
    }
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40,167,69,0.3);
    }
    .selected-count {
        color: #667eea;
        font-weight: 600;
        font-size: 0.95em;
    }
    .reports-container {
        display: grid;
        gap: 25px;
    }
    .report-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    .report-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        border-color: #667eea;
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }
    .report-info {
        flex: 1;
    }
    .header-controls {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    .checkbox-container {
        position: relative;
        cursor: pointer;
        font-size: 18px;
        user-select: none;
    }
    .checkbox-container input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }
    .checkmark {
        position: relative;
        display: inline-block;
        height: 20px;
        width: 20px;
        background-color: rgba(255,255,255,0.2);
        border: 2px solid rgba(255,255,255,0.5);
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    .checkbox-container:hover .checkmark {
        background-color: rgba(255,255,255,0.3);
        border-color: rgba(255,255,255,0.8);
    }
    .checkbox-container input:checked ~ .checkmark {
        background-color: #28a745;
        border-color: #28a745;
    }
    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    .checkbox-container input:checked ~ .checkmark:after {
        display: block;
    }
    .report-time {
        font-size: 1.1em;
        font-weight: 600;
    }
    .report-date {
        font-size: 0.9em;
        opacity: 0.9;
    }
    .devices-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
        padding: 25px;
    }
    .device-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .device-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    .device-item.success {
        border-left-color: #28a745;
    }
    .device-item.danger {
        border-left-color: #dc3545;
    }
    .device-name {
        font-weight: 700;
        font-size: 1.1em;
        color: #2c3e50;
    }
    .device-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
    }
    .device-status.success {
        color: #28a745;
    }
    .device-status.danger {
        color: #dc3545;
    }
    .device-note {
        font-size: 0.85em;
        color: #6c757d;
        margin-top: 5px;
        font-style: italic;
    }
    .no-data {
        text-align: center;
        padding: 80px 20px;
        color: #6c757d;
    }
    .no-data-icon {
        font-size: 4em;
        margin-bottom: 20px;
        color: #667eea;
    }
    .no-data-text {
        font-size: 1.3em;
        margin-bottom: 10px;
    }
    .expand-icon {
        transition: transform 0.3s ease;
        font-size: 1.2em;
        color: rgba(255,255,255,0.8);
    }
    .report-card.expanded .expand-icon {
        transform: rotate(180deg);
    }
    .report-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    .report-card.expanded .report-content {
        max-height: 2000px;
    }
    .last-update {
        text-align: center;
        margin-top: 30px;
        color: #6c757d;
        font-size: 0.9em;
        padding: 15px;
        background: rgba(108,117,125,0.1);
        border-radius: 10px;
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .central-status-section {
            margin: 15px;
            padding: 20px;
        }
        .page-title {
            font-size: 2em;
        }
        .controls-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 15px;
        }
        .selection-controls {
            justify-content: center;
        }
        .btn-print {
            width: 100%;
            text-align: center;
        }
        .devices-grid {
            grid-template-columns: 1fr;
            padding: 20px;
        }
        .report-header {
            padding: 15px 20px;
        }
        .report-info {
            flex: 1;
        }
        .header-controls {
            gap: 10px;
        }
        .device-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        .device-status {
            align-self: flex-end;
        }
    }

    @media (max-width: 480px) {
        .stats-container {
            grid-template-columns: 1fr;
        }
        .page-title {
            font-size: 1.6em;
        }
        .stat-number {
            font-size: 1.8em;
        }
        .report-time {
            font-size: 1em;
        }
        .device-name {
            font-size: 1em;
        }
    }

    /* تحسينات إضافية */
    @supports not (backdrop-filter: blur(10px)) {
        .central-status-section {
            background: rgba(255,255,255,0.98);
        }
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .report-card {
        animation: fadeInUp 0.6s ease forwards;
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .central-status-section {
            margin: 20px 15px 0 15px;
            padding: 25px 15px 20px 15px;
            max-width: none;
        }
        .central-status-section h3 {
            font-size: 1.6em;
        }
        .stats-bar {
            flex-direction: column;
            gap: 15px;
            padding: 20px 15px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .stats-item:last-child {
            border-bottom: none;
        }
        .time-header {
            flex-direction: column;
            gap: 5px;
            text-align: center;
            padding: 15px;
        }
        .central-table {
            font-size: 0.9em;
        }
        .central-table th,
        .central-table td {
            padding: 12px 8px;
        }
    }

    @media (max-width: 480px) {
        .central-table {
            font-size: 0.8em;
        }
        .central-table th,
        .central-table td {
            padding: 10px 6px;
        }
        .stats-number {
            font-size: 1.4em;
        }
        .device-name {
            font-size: 1em;
        }
    }

    /* تحسينات إضافية */
    @supports not (backdrop-filter: blur(10px)) {
        .central-status-section {
            background: rgba(255,255,255,0.98);
        }
    }
</style>

<div class="central-status-section">
    <h1 class="page-title">📋 مواقف اتصالات المركزية</h1>

    <?php if (empty($statuses)) { ?>
        <div class="no-data">
            <div class="no-data-icon">📭</div>
            <div class="no-data-text">لا توجد مواقف مسجلة خلال آخر 24 ساعة</div>
            <small>آخر تحديث: <?= date('Y-m-d H:i:s') ?></small>
        </div>
    <?php } else { ?>
        <!-- أدوات التحكم -->
        <div class="controls-bar">
            <div class="selection-controls">
                <button type="button" class="btn-control" onclick="selectAll()">تحديد الكل</button>
                <button type="button" class="btn-control" onclick="deselectAll()">إلغاء التحديد</button>
                <span class="selected-count">المحدد: <span id="selectedCount">0</span></span>
            </div>
            <button type="button" class="btn-print" onclick="printSelected()">
                🖨️ طباعة المحدد
            </button>
        </div>

        <!-- التقارير -->
        <div class="reports-container">
            <?php foreach ($statuses as $sent_at => $rows) { ?>
                <div class="report-card" data-time="<?= $sent_at ?>">
                    <div class="report-header" onclick="toggleReport(this.parentElement)">
                        <div class="report-info">
                            <div class="report-time">📅 <?= date('Y-m-d', strtotime($sent_at)) ?></div>
                            <div class="report-date">🕐 <?= date('H:i:s', strtotime($sent_at)) ?></div>
                        </div>
                        <div class="header-controls">
                            <label class="checkbox-container" onclick="event.stopPropagation()">
                                <input type="checkbox" class="report-checkbox" onchange="updateSelectedCount()">
                                <span class="checkmark"></span>
                            </label>
                            <div class="expand-icon">▼</div>
                        </div>
                    </div>

                    <div class="report-content">
                        <div class="devices-grid">
                            <?php foreach ($rows as $row) {
                                $status_class = ($row['status'] == 'تم الرد') ? 'success' : 'danger';
                                $status_icon = ($row['status'] == 'تم الرد') ? '✅' : '❌';
                            ?>
                                <div class="device-item <?= $status_class ?>">
                                    <div>
                                        <div class="device-name"><?= htmlspecialchars($row['device']) ?></div>
                                        <?php if (!empty($row['note'])) { ?>
                                            <div class="device-note"><?= htmlspecialchars($row['note']) ?></div>
                                        <?php } ?>
                                    </div>
                                    <div class="device-status <?= $status_class ?>">
                                        <?= $status_icon ?> <?= htmlspecialchars($row['status']) ?>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>

        <div class="last-update">
            آخر تحديث: <?= date('Y-m-d H:i:s') ?> |
            عرض البيانات من: <?= date('Y-m-d H:i:s', strtotime('-24 hours')) ?>
        </div>
    <?php } ?>
</div>

<script>
function toggleReport(element) {
    element.classList.toggle('expanded');
}

// تحديد جميع التقارير
function selectAll() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

// إلغاء تحديد جميع التقارير
function deselectAll() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// تحديث عداد المحدد
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.report-checkbox');
    const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    document.getElementById('selectedCount').textContent = selectedCount;
}

// طباعة التقارير المحددة
function printSelected() {
    const selectedReports = [];
    const checkboxes = document.querySelectorAll('.report-checkbox');

    checkboxes.forEach((checkbox, index) => {
        if (checkbox.checked) {
            const reportCard = checkbox.closest('.report-card');
            selectedReports.push(reportCard);
        }
    });

    if (selectedReports.length === 0) {
        alert('يرجى تحديد تقرير واحد على الأقل للطباعة');
        return;
    }

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent(selectedReports);

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// إنشاء محتوى الطباعة
function generatePrintContent(reports) {
    let content = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير مواقف اتصالات المركزية</title>
        <style>
            body {
                font-family: 'Cairo', Tahoma, Arial, sans-serif;
                margin: 20px;
                direction: rtl;
                color: #333;
            }
            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #667eea;
                padding-bottom: 20px;
            }
            .print-title {
                font-size: 24px;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 10px;
            }
            .print-date {
                color: #666;
                font-size: 14px;
            }
            .report-section {
                margin-bottom: 30px;
                page-break-inside: avoid;
            }
            .report-title {
                background: #667eea;
                color: white;
                padding: 10px 15px;
                font-weight: bold;
                border-radius: 5px;
                margin-bottom: 15px;
            }
            .devices-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .devices-table th, .devices-table td {
                border: 1px solid #ddd;
                padding: 8px 12px;
                text-align: center;
            }
            .devices-table th {
                background: #f8f9fa;
                font-weight: bold;
            }
            .status-success { color: #28a745; font-weight: bold; }
            .status-danger { color: #dc3545; font-weight: bold; }
            @media print {
                body { margin: 0; }
                .report-section { page-break-inside: avoid; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <div class="print-title">📋 تقرير مواقف اتصالات المركزية</div>
            <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</div>
        </div>
    `;

    reports.forEach(report => {
        const timeAttr = report.getAttribute('data-time');
        const reportTime = new Date(timeAttr).toLocaleString('ar-SA');
        const devices = report.querySelectorAll('.device-item');

        content += `
        <div class="report-section">
            <div class="report-title">📅 ${reportTime}</div>
            <table class="devices-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الجهاز</th>
                        <th>حالة الاتصال</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        devices.forEach((device, index) => {
            const deviceName = device.querySelector('.device-name').textContent;
            const deviceStatus = device.querySelector('.device-status').textContent;
            const deviceNote = device.querySelector('.device-note');
            const note = deviceNote ? deviceNote.textContent : 'لا توجد ملاحظة';
            const statusClass = device.classList.contains('success') ? 'status-success' : 'status-danger';

            content += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${deviceName}</td>
                    <td class="${statusClass}">${deviceStatus}</td>
                    <td>${note}</td>
                </tr>
            `;
        });

        content += `
                </tbody>
            </table>
        </div>
        `;
    });

    content += `
    </body>
    </html>
    `;

    return content;
}

// تحديث الصفحة كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);

// تأثيرات الظهور
document.addEventListener('DOMContentLoaded', function() {
    const reportCards = document.querySelectorAll('.report-card');
    reportCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // تحديث العداد في البداية
    updateSelectedCount();
});
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
