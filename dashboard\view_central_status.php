<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("عرض مواقف اتصالات المركزية", false, "index.php");

// جلب المواقف من قاعدة البيانات
$statuses = [];
include '../config/db.php';
$res = $conn->query("SELECT sent_at, device, status, note FROM central_status ORDER BY sent_at DESC, id ASC");
if ($res) {
    while ($row = $res->fetch_assoc()) {
        $statuses[$row['sent_at']][] = $row;
    }
}
$conn->close();
?>

<style>
    .central-status-section {
        background: #fff;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(30,60,114,0.13);
        padding: 38px 22px 28px 22px;
        margin: 48px auto 0 auto;
        max-width: 950px;
        border: 1.5px solid #1da1f2;
        text-align: center;
    }
    .central-status-section h3 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 18px;
    }
    .central-table {
        margin: 0 auto 18px auto;
        width: 100%;
        background: #f7fafd;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(29,161,242,0.07);
    }
    .central-table th {
        background: #1da1f2;
        color: #fff;
        font-size: 1.05em;
        font-weight: bold;
    }
    .central-table td {
        color: #232526;
        background: #eaf6fb;
        font-size: 1em;
    }
    .central-table tr:nth-child(even) td {
        background: #d6eaf8;
    }
</style>

<div class="central-status-section">
    <h3>📋 جميع مواقف اتصالات المركزية</h3>
    <?php if (empty($statuses)) { ?>
        <div class="alert alert-info">لا توجد مواقف مسجلة حتى الآن.</div>
    <?php } else { ?>
        <?php foreach ($statuses as $sent_at => $rows) { ?>
            <div style="margin-bottom: 32px;">
                <div style="color:#28a745;font-weight:bold;">وقت الإرسال: <?= htmlspecialchars($sent_at) ?></div>
                <table class="central-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الجهاز</th>
                            <th>الحالة</th>
                            <th>ملاحظة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        foreach ($rows as $i => $row) {
                            echo "<tr>";
                            echo "<td>" . ($i + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($row['device']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['note']) . "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        <?php } ?>
    <?php } ?>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
