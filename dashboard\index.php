<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("الإدارة العامة - الرصين", false, "index.php");
include '../config/db.php';
// جلب إحصائيات التكتات
$total_count = 0;
$open_count = 0;
$closed_count = 0;
$res = $conn->query("SELECT COUNT(*) as total FROM tickets");
if ($res) {
    $row = $res->fetch_assoc();
    $total_count = $row['total'];
}
$res2 = $conn->query("SELECT COUNT(*) as open FROM tickets WHERE status = 'مفتوحة'");
if ($res2) {
    $row2 = $res2->fetch_assoc();
    $open_count = $row2['open'];
}
$res3 = $conn->query("SELECT COUNT(*) as closed FROM tickets WHERE status = 'مغلقة'");
if ($res3) {
    $row3 = $res3->fetch_assoc();
    $closed_count = $row3['closed'];
}

// جلب إحصائيات المواقف اليومية
$today_reports = 0;
$total_reports = 0;
$res4 = $conn->query("SELECT COUNT(*) as today FROM central_status WHERE DATE(sent_at) = CURDATE()");
if ($res4) {
    $row4 = $res4->fetch_assoc();
    $today_reports = $row4['today'];
}
$res5 = $conn->query("SELECT COUNT(*) as total FROM central_status");
if ($res5) {
    $row5 = $res5->fetch_assoc();
    $total_reports = $row5['total'];
}

// التأكد من وجود حقل sender_name في جدول tickets
$check_column = $conn->query("SHOW COLUMNS FROM tickets LIKE 'sender_name'");
if (!$check_column || $check_column->num_rows == 0) {
    $conn->query("ALTER TABLE tickets ADD COLUMN sender_name VARCHAR(100) DEFAULT NULL AFTER title");
}

// جلب آخر التحديثات للإشعارات
$notifications = [];

// إشعارات التذاكر
$res6 = $conn->query("SELECT title, sender_name, created_at, 'ticket' as type FROM tickets ORDER BY created_at DESC LIMIT 2");
if ($res6) {
    while ($row6 = $res6->fetch_assoc()) {
        $sender = $row6['sender_name'] ?? 'غير محدد';
        if (empty($sender) || $sender === 'غير محدد') {
            $sender = 'مختبر الاتصالات'; // قيمة افتراضية
        }
        $notifications[] = [
            'type' => 'ticket',
            'message' => 'تم استلام كتاب مرفوع من ' . $sender,
            'details' => $row6['title'],
            'time' => $row6['created_at'],
            'icon' => '📨',
            'color' => '#1DA1F2'
        ];
    }
}

// إشعارات المواقف
$res7 = $conn->query("SELECT device, status, sent_at FROM central_status ORDER BY sent_at DESC LIMIT 2");
if ($res7) {
    while ($row7 = $res7->fetch_assoc()) {
        $notifications[] = [
            'type' => 'status',
            'message' => 'تم استلام موقف من مختبر الاتصالات المركزية',
            'details' => 'جهاز ' . $row7['device'] . ' - الحالة: ' . $row7['status'],
            'time' => $row7['sent_at'],
            'icon' => '📡',
            'color' => '#28a745'
        ];
    }
}

// إشعارات الإنجازات (إذا كان الجدول موجود)
$table_check = $conn->query("SHOW TABLES LIKE 'achievements'");
if ($table_check && $table_check->num_rows > 0) {
    $res8 = $conn->query("SELECT achievement_title, submitted_by, unit_name, created_at FROM achievements ORDER BY created_at DESC LIMIT 2");
    if ($res8) {
        while ($row8 = $res8->fetch_assoc()) {
            $sender = $row8['submitted_by'] ?? $row8['unit_name'] ?? 'غير محدد';
            if (empty($sender) || $sender === 'غير محدد') {
                $sender = 'إحدى الوحدات التابعة';
            }
            $notifications[] = [
                'type' => 'achievement',
                'message' => 'تم استلام إنجاز من ' . $sender,
                'details' => $row8['achievement_title'],
                'time' => $row8['created_at'],
                'icon' => '🏆',
                'color' => '#ffc107'
            ];
        }
    }
}

// إشعارات الوحدات الجديدة (إذا كان الجدول موجود)
$table_check2 = $conn->query("SHOW TABLES LIKE 'units'");
if ($table_check2 && $table_check2->num_rows > 0) {
    $res9 = $conn->query("SELECT unit_name, unit_type, commander_name, created_at FROM units ORDER BY created_at DESC LIMIT 1");
    if ($res9) {
        while ($row9 = $res9->fetch_assoc()) {
            $commander = $row9['commander_name'] ?? 'غير محدد';
            $notifications[] = [
                'type' => 'unit',
                'message' => 'تم تسجيل ' . $row9['unit_type'] . ' جديدة في النظام',
                'details' => $row9['unit_name'] . ($commander !== 'غير محدد' ? ' - القائد: ' . $commander : ''),
                'time' => $row9['created_at'],
                'icon' => '🏢',
                'color' => '#17a2b8'
            ];
        }
    }
}

// إضافة إشعارات تجريبية إذا لم توجد إشعارات
if (empty($notifications)) {
    $notifications = [
        [
            'type' => 'status',
            'message' => 'تم استلام موقف من مختبر الاتصالات المركزية',
            'details' => 'جميع الأجهزة تعمل بشكل طبيعي - آخر تحديث',
            'time' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
            'icon' => '📡',
            'color' => '#28a745'
        ],
        [
            'type' => 'ticket',
            'message' => 'تم استلام كتاب مرفوع من مختبر الاتصالات',
            'details' => 'طلب صيانة دورية للأجهزة الرئيسية',
            'time' => date('Y-m-d H:i:s', strtotime('-1 hour')),
            'icon' => '📨',
            'color' => '#1DA1F2'
        ],
        [
            'type' => 'achievement',
            'message' => 'تم استلام إنجاز من الوحدة الفنية',
            'details' => 'إنجاز مهمة تطوير شبكة الاتصالات الداخلية',
            'time' => date('Y-m-d H:i:s', strtotime('-2 hours')),
            'icon' => '🏆',
            'color' => '#ffc107'
        ],
        [
            'type' => 'unit',
            'message' => 'تم تسجيل وحدة جديدة في النظام',
            'details' => 'شعبة الدعم التقني - القائد: العقيد أحمد محمد',
            'time' => date('Y-m-d H:i:s', strtotime('-3 hours')),
            'icon' => '🏢',
            'color' => '#17a2b8'
        ]
    ];
} else {
    // ترتيب الإشعارات حسب التاريخ
    usort($notifications, function($a, $b) {
        return strtotime($b['time']) - strtotime($a['time']);
    });

    // أخذ أحدث 5 إشعارات فقط
    $notifications = array_slice($notifications, 0, 5);
}

// استقبال موقف الاتصالات من POST وتخزينه في قاعدة البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    include '../config/db.php';
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'];
    $notes = $_POST['note'] ?? [];
    $time = date('Y-m-d H:i:s');
    $table_check = $conn->query("SHOW TABLES LIKE 'central_status'");
    if (!$table_check || $table_check->num_rows == 0) {
        $create = "CREATE TABLE IF NOT EXISTS central_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device VARCHAR(32) NOT NULL,
            status VARCHAR(32) NOT NULL,
            note VARCHAR(255) DEFAULT NULL,
            sent_at DATETIME NOT NULL
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
        $conn->query($create);
    }
    foreach ($devices as $device) {
        $status = $statuses[$device] ?? '';
        $note = $notes[$device] ?? '';
        $stmt = $conn->prepare("INSERT INTO central_status (device, status, note, sent_at) VALUES (?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("ssss", $device, $status, $note, $time);
            $stmt->execute();
            $stmt->close();
        }
    }
    $conn->close();
    header('Location: view_central_status.php');
    exit;
}
?>
<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
    .btn-outline-primary, .btn-custom {
        color: #1DA1F2;
        border-color: #1DA1F2;
        background: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .btn-outline-primary:hover, .btn-custom:hover {
        background-color: #1DA1F2;
        color: white;
    }
    .btn-secondary, .btn-info {
        border-radius: 30px;
        font-weight: bold;
    }
    .logout-btn {
        float: left;
        margin-bottom: 20px;
    }
    .notifications-panel {
        background: #1E2124;
        border-radius: 15px;
        border: 1px solid #333;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 30px;
    }
    .notifications-header {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
        font-weight: bold;
    }
    .notification-item {
        padding: 15px 20px;
        border-bottom: 1px solid #333;
        color: #fff;
        transition: all 0.3s ease;
        position: relative;
    }
    .notification-item:hover {
        background: #2a2d31;
        transform: translateX(-5px);
    }
    .notification-item:last-child {
        border-bottom: none;
        border-radius: 0 0 15px 15px;
    }
    .notification-icon {
        font-size: 1.5em;
        margin-left: 15px;
        float: right;
    }
    .notification-content {
        margin-right: 60px;
    }
    .notification-message {
        font-weight: bold;
        margin-bottom: 5px;
        font-size: 1em;
    }
    .notification-details {
        color: #aaa;
        font-size: 0.9em;
        margin-bottom: 8px;
        line-height: 1.4;
    }
    .notification-time {
        font-size: 0.8em;
        color: #666;
        font-style: italic;
    }
    .notification-badge {
        position: absolute;
        top: 10px;
        left: 15px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #1DA1F2;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        transition: transform 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-3px);
    }
</style>
<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= isset($_SESSION['user']['ar_name']) ? htmlspecialchars($_SESSION['user']['ar_name']) : '' ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم الإدارة العامة - نظام الرصين</p>

    <!-- قسم الإشعارات والتنبيهات -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="notifications-panel">
                <div class="notifications-header">
                    🔔 الإشعارات والتحديثات الأخيرة
                </div>
                <div class="notifications-body">
                    <?php if (!empty($notifications)): ?>
                        <?php foreach ($notifications as $notification): ?>
                            <div class="notification-item">
                                <div class="notification-badge" style="background: <?= $notification['color'] ?>;"></div>
                                <div class="notification-icon" style="color: <?= $notification['color'] ?>;">
                                    <?= $notification['icon'] ?>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-message" style="color: <?= $notification['color'] ?>;">
                                        <?= htmlspecialchars($notification['message']) ?>
                                    </div>
                                    <div class="notification-details">
                                        <?= htmlspecialchars($notification['details']) ?>
                                    </div>
                                    <div class="notification-time">
                                        <?php
                                        $time = strtotime($notification['time']);
                                        $now = time();
                                        $diff = $now - $time;

                                        if ($diff < 3600) {
                                            echo 'منذ ' . floor($diff / 60) . ' دقيقة';
                                        } elseif ($diff < 86400) {
                                            echo 'منذ ' . floor($diff / 3600) . ' ساعة';
                                        } else {
                                            echo date('Y/m/d - h:i A', $time);
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="notification-item text-center" style="color: #666; padding: 40px 20px;">
                            <div style="font-size: 2em; margin-bottom: 10px;">🔕</div>
                            <div>لا توجد إشعارات جديدة</div>
                            <div style="font-size: 0.8em; margin-top: 5px;">ستظهر الإشعارات هنا عند وصول تحديثات جديدة</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card stats-card text-center p-4 mb-3">
                <div style="font-size:2.5em;">📊</div>
                <div style="font-size:1.1em; margin-bottom: 10px;">مواقف اليوم</div>
                <div style="font-size:2.2em; font-weight:bold;"><?= $today_reports ?></div>
            </div>
            <div class="card stats-card text-center p-4">
                <div style="font-size:2.5em;">📈</div>
                <div style="font-size:1.1em; margin-bottom: 10px;">إجمالي المواقف</div>
                <div style="font-size:2.2em; font-weight:bold;"><?= $total_reports ?></div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التذاكر -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📬</div>
                    <div style="font-size:1.2em;">مجموع التذاكر</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #ffc107; color: #222; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">⏳</div>
                    <div style="font-size:1.2em;">التذاكر المفتوحة</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $open_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">✅</div>
                    <div style="font-size:1.2em;">التذاكر المغلقة</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $closed_count ?></div>
                </div>
            </div>
        </div>
    </div>
    <!-- الوظائف الرئيسية -->
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 عرض التذاكر</h5>
                <p class="mb-3">مشاهدة التذاكر الواردة والصادرة</p>
                <a href="/rasseem/dashboard/tickets/all.php" class="btn btn-outline-success">عرض التذاكر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📁 عرض المواقف</h5>
                <p class="mb-3">مراجعة المواقف اليومية من المخابر</p>
                <a href="/rasseem/dashboard/view_central_status.php" class="btn btn-outline-warning">عرض المواقف</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🏆 الإنجازات المستلمة</h5>
                <p class="mb-3">عرض إنجازات الوحدات والشعب التابعة</p>
                <a href="/rasseem/dashboard/achievements.php" class="btn btn-outline-primary">عرض الإنجازات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🏢 إدارة الوحدات</h5>
                <p class="mb-3">إدارة الوحدات والشعب التابعة للقسم</p>
                <a href="/rasseem/dashboard/units.php" class="btn btn-outline-info">إدارة الوحدات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📊 التقارير الإحصائية</h5>
                <p class="mb-3">عرض التقارير والإحصائيات الشاملة</p>
                <a href="/rasseem/dashboard/reports.php" class="btn btn-outline-secondary">عرض التقارير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>⚙️ إعدادات النظام</h5>
                <p class="mb-3">إدارة إعدادات النظام والمستخدمين</p>
                <a href="/rasseem/dashboard/settings.php" class="btn btn-outline-danger">الإعدادات</a>
            </div>
        </div>
    </div>
</div>
<?php
include '../includes/footer.php';
renderFooter();
?>