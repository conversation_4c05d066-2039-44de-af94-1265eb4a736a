<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("الإدارة العامة - الرصين", false, "index.php");
include '../config/db.php';
// جلب إحصائيات التكتات
$total_count = 0;
$open_count = 0;
$closed_count = 0;
$res = $conn->query("SELECT COUNT(*) as total FROM tickets");
if ($res) {
    $row = $res->fetch_assoc();
    $total_count = $row['total'];
}
$res2 = $conn->query("SELECT COUNT(*) as open FROM tickets WHERE status = 'مفتوحة'");
if ($res2) {
    $row2 = $res2->fetch_assoc();
    $open_count = $row2['open'];
}
$res3 = $conn->query("SELECT COUNT(*) as closed FROM tickets WHERE status = 'مغلقة'");
if ($res3) {
    $row3 = $res3->fetch_assoc();
    $closed_count = $row3['closed'];
}

// جلب إحصائيات المواقف اليومية
$today_reports = 0;
$total_reports = 0;
$res4 = $conn->query("SELECT COUNT(*) as today FROM central_status WHERE DATE(sent_at) = CURDATE()");
if ($res4) {
    $row4 = $res4->fetch_assoc();
    $today_reports = $row4['today'];
}
$res5 = $conn->query("SELECT COUNT(*) as total FROM central_status");
if ($res5) {
    $row5 = $res5->fetch_assoc();
    $total_reports = $row5['total'];
}

// جلب آخر التحديثات للإشعارات
$recent_tickets = [];
$res6 = $conn->query("SELECT title, created_at FROM tickets ORDER BY created_at DESC LIMIT 3");
if ($res6) {
    while ($row6 = $res6->fetch_assoc()) {
        $recent_tickets[] = $row6;
    }
}

$recent_reports = [];
$res7 = $conn->query("SELECT device, status, sent_at FROM central_status ORDER BY sent_at DESC LIMIT 3");
if ($res7) {
    while ($row7 = $res7->fetch_assoc()) {
        $recent_reports[] = $row7;
    }
}

// استقبال موقف الاتصالات من POST وتخزينه في قاعدة البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    include '../config/db.php';
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'];
    $notes = $_POST['note'] ?? [];
    $time = date('Y-m-d H:i:s');
    $table_check = $conn->query("SHOW TABLES LIKE 'central_status'");
    if (!$table_check || $table_check->num_rows == 0) {
        $create = "CREATE TABLE IF NOT EXISTS central_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device VARCHAR(32) NOT NULL,
            status VARCHAR(32) NOT NULL,
            note VARCHAR(255) DEFAULT NULL,
            sent_at DATETIME NOT NULL
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
        $conn->query($create);
    }
    foreach ($devices as $device) {
        $status = $statuses[$device] ?? '';
        $note = $notes[$device] ?? '';
        $stmt = $conn->prepare("INSERT INTO central_status (device, status, note, sent_at) VALUES (?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("ssss", $device, $status, $note, $time);
            $stmt->execute();
            $stmt->close();
        }
    }
    $conn->close();
    header('Location: view_central_status.php');
    exit;
}
?>
<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
    .btn-outline-primary, .btn-custom {
        color: #1DA1F2;
        border-color: #1DA1F2;
        background: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .btn-outline-primary:hover, .btn-custom:hover {
        background-color: #1DA1F2;
        color: white;
    }
    .btn-secondary, .btn-info {
        border-radius: 30px;
        font-weight: bold;
    }
    .logout-btn {
        float: left;
        margin-bottom: 20px;
    }
    .notifications-panel {
        background: #1E2124;
        border-radius: 15px;
        border: 1px solid #333;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 30px;
    }
    .notifications-header {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
        font-weight: bold;
    }
    .notification-item {
        padding: 12px 20px;
        border-bottom: 1px solid #333;
        color: #aaa;
        transition: all 0.3s ease;
    }
    .notification-item:hover {
        background: #2a2d31;
        color: #fff;
    }
    .notification-item:last-child {
        border-bottom: none;
        border-radius: 0 0 15px 15px;
    }
    .notification-time {
        font-size: 0.8em;
        color: #666;
        float: left;
    }
    .notification-text {
        margin-bottom: 5px;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        transition: transform 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-3px);
    }
</style>
<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= isset($_SESSION['user']['ar_name']) ? htmlspecialchars($_SESSION['user']['ar_name']) : '' ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم الإدارة العامة - نظام الرصين</p>

    <!-- قسم الإشعارات والتنبيهات -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="notifications-panel">
                <div class="notifications-header">
                    🔔 الإشعارات والتحديثات الأخيرة
                </div>
                <div class="notifications-body">
                    <?php if (!empty($recent_tickets)): ?>
                        <div style="padding: 15px 20px; border-bottom: 1px solid #333; color: #1DA1F2; font-weight: bold;">
                            📩 آخر التذاكر المستلمة
                        </div>
                        <?php foreach ($recent_tickets as $ticket): ?>
                            <div class="notification-item">
                                <div class="notification-text">
                                    <strong>تذكرة جديدة:</strong> <?= htmlspecialchars($ticket['title']) ?>
                                </div>
                                <div class="notification-time">
                                    <?= date('Y/m/d - h:i A', strtotime($ticket['created_at'])) ?>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if (!empty($recent_reports)): ?>
                        <div style="padding: 15px 20px; border-bottom: 1px solid #333; color: #28a745; font-weight: bold;">
                            📊 آخر المواقف المستلمة
                        </div>
                        <?php foreach ($recent_reports as $report): ?>
                            <div class="notification-item">
                                <div class="notification-text">
                                    <strong>موقف جهاز <?= htmlspecialchars($report['device']) ?>:</strong> <?= htmlspecialchars($report['status']) ?>
                                </div>
                                <div class="notification-time">
                                    <?= date('Y/m/d - h:i A', strtotime($report['sent_at'])) ?>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if (empty($recent_tickets) && empty($recent_reports)): ?>
                        <div class="notification-item text-center" style="color: #666;">
                            لا توجد إشعارات جديدة
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card stats-card text-center p-4 mb-3">
                <div style="font-size:2.5em;">📊</div>
                <div style="font-size:1.1em; margin-bottom: 10px;">مواقف اليوم</div>
                <div style="font-size:2.2em; font-weight:bold;"><?= $today_reports ?></div>
            </div>
            <div class="card stats-card text-center p-4">
                <div style="font-size:2.5em;">📈</div>
                <div style="font-size:1.1em; margin-bottom: 10px;">إجمالي المواقف</div>
                <div style="font-size:2.2em; font-weight:bold;"><?= $total_reports ?></div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التذاكر -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📬</div>
                    <div style="font-size:1.2em;">مجموع التذاكر</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #ffc107; color: #222; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">⏳</div>
                    <div style="font-size:1.2em;">التذاكر المفتوحة</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $open_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">✅</div>
                    <div style="font-size:1.2em;">التذاكر المغلقة</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $closed_count ?></div>
                </div>
            </div>
        </div>
    </div>
    <!-- الوظائف الرئيسية -->
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 عرض التذاكر</h5>
                <p class="mb-3">مشاهدة التذاكر الواردة والصادرة</p>
                <a href="/rasseem/dashboard/tickets/all.php" class="btn btn-outline-success">عرض التذاكر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📁 عرض المواقف</h5>
                <p class="mb-3">مراجعة المواقف اليومية من المخابر</p>
                <a href="/rasseem/dashboard/view_central_status.php" class="btn btn-outline-warning">عرض المواقف</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🏆 الإنجازات المستلمة</h5>
                <p class="mb-3">عرض إنجازات الوحدات والشعب التابعة</p>
                <a href="/rasseem/dashboard/achievements.php" class="btn btn-outline-primary">عرض الإنجازات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🏢 إدارة الوحدات</h5>
                <p class="mb-3">إدارة الوحدات والشعب التابعة للقسم</p>
                <a href="/rasseem/dashboard/units.php" class="btn btn-outline-info">إدارة الوحدات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📊 التقارير الإحصائية</h5>
                <p class="mb-3">عرض التقارير والإحصائيات الشاملة</p>
                <a href="/rasseem/dashboard/reports.php" class="btn btn-outline-secondary">عرض التقارير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>⚙️ إعدادات النظام</h5>
                <p class="mb-3">إدارة إعدادات النظام والمستخدمين</p>
                <a href="/rasseem/dashboard/settings.php" class="btn btn-outline-danger">الإعدادات</a>
            </div>
        </div>
    </div>
</div>
<?php
include '../includes/footer.php';
renderFooter();
?>