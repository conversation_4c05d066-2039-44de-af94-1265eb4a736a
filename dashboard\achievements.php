<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("الإنجازات المستلمة - الرصين", false, "../index.php");
include '../config/db.php';

// إنشاء جدول الإنجازات إذا لم يكن موجوداً
$table_check = $conn->query("SHOW TABLES LIKE 'achievements'");
if (!$table_check || $table_check->num_rows == 0) {
    $create_table = "CREATE TABLE IF NOT EXISTS achievements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        unit_name VARCHAR(100) NOT NULL,
        achievement_title VARCHAR(200) NOT NULL,
        achievement_description TEXT,
        achievement_date DATE NOT NULL,
        submitted_by VARCHAR(100),
        status ENUM('جديد', 'قيد المراجعة', 'مقبول', 'مرفوض') DEFAULT 'جديد',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
    $conn->query($create_table);
}

// معالجة إضافة إنجاز جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_achievement'])) {
    $unit_name = $_POST['unit_name'] ?? '';
    $achievement_title = $_POST['achievement_title'] ?? '';
    $achievement_description = $_POST['achievement_description'] ?? '';
    $achievement_date = $_POST['achievement_date'] ?? '';
    $submitted_by = $_POST['submitted_by'] ?? '';
    
    if (!empty($unit_name) && !empty($achievement_title) && !empty($achievement_date)) {
        $stmt = $conn->prepare("INSERT INTO achievements (unit_name, achievement_title, achievement_description, achievement_date, submitted_by) VALUES (?, ?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("sssss", $unit_name, $achievement_title, $achievement_description, $achievement_date, $submitted_by);
            $stmt->execute();
            $stmt->close();
            $success_message = "تم إضافة الإنجاز بنجاح!";
        }
    } else {
        $error_message = "يرجى ملء جميع الحقول المطلوبة!";
    }
}

// معالجة تحديث حالة الإنجاز
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $achievement_id = $_POST['achievement_id'] ?? '';
    $new_status = $_POST['new_status'] ?? '';
    
    if (!empty($achievement_id) && !empty($new_status)) {
        $stmt = $conn->prepare("UPDATE achievements SET status = ? WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("si", $new_status, $achievement_id);
            $stmt->execute();
            $stmt->close();
            $update_message = "تم تحديث حالة الإنجاز بنجاح!";
        }
    }
}

// جلب الإنجازات
$achievements = [];
$result = $conn->query("SELECT * FROM achievements ORDER BY created_at DESC");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $achievements[] = $row;
    }
}

// إحصائيات الإنجازات
$stats = [
    'total' => 0,
    'new' => 0,
    'under_review' => 0,
    'accepted' => 0,
    'rejected' => 0
];

$stats_result = $conn->query("SELECT status, COUNT(*) as count FROM achievements GROUP BY status");
if ($stats_result) {
    while ($row = $stats_result->fetch_assoc()) {
        $stats['total'] += $row['count'];
        switch ($row['status']) {
            case 'جديد':
                $stats['new'] = $row['count'];
                break;
            case 'قيد المراجعة':
                $stats['under_review'] = $row['count'];
                break;
            case 'مقبول':
                $stats['accepted'] = $row['count'];
                break;
            case 'مرفوض':
                $stats['rejected'] = $row['count'];
                break;
        }
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .page-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
        text-align: center;
    }
    .stats-row {
        margin-bottom: 30px;
    }
    .stat-card {
        background: #1E2124;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        color: white;
        border: 1px solid #333;
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .stat-label {
        font-size: 1.1em;
        opacity: 0.8;
    }
    .card-custom {
        background: #1E2124;
        border: 1px solid #333;
        border-radius: 15px;
        color: white;
        margin-bottom: 20px;
    }
    .card-header {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border-radius: 15px 15px 0 0;
        padding: 15px 20px;
        font-weight: bold;
    }
    .form-control, .form-select {
        background: #2a2d31;
        border: 1px solid #444;
        color: white;
        border-radius: 8px;
    }
    .form-control:focus, .form-select:focus {
        background: #2a2d31;
        border-color: #1DA1F2;
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border: none;
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: bold;
    }
    .btn-outline-primary {
        color: #1DA1F2;
        border-color: #1DA1F2;
        border-radius: 25px;
        padding: 8px 20px;
    }
    .btn-outline-primary:hover {
        background: #1DA1F2;
        color: white;
    }
    .table-dark {
        background: #1E2124;
        border-radius: 10px;
        overflow: hidden;
    }
    .table-dark th {
        background: #2a2d31;
        border-color: #444;
    }
    .table-dark td {
        border-color: #444;
    }
    .status-badge {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .status-new { background: #ffc107; color: #000; }
    .status-review { background: #17a2b8; color: #fff; }
    .status-accepted { background: #28a745; color: #fff; }
    .status-rejected { background: #dc3545; color: #fff; }
    .alert-success {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        border-radius: 10px;
    }
    .alert-danger {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
        border-radius: 10px;
    }
</style>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="page-title">🏆 الإنجازات المستلمة من الوحدات والشعب</h1>
        <a href="/rasseem/dashboard/index.php" class="btn btn-outline-primary">
            🏠 العودة للرئيسية
        </a>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success"><?= $success_message ?></div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger"><?= $error_message ?></div>
    <?php endif; ?>
    
    <?php if (isset($update_message)): ?>
        <div class="alert alert-success"><?= $update_message ?></div>
    <?php endif; ?>

    <!-- إحصائيات الإنجازات -->
    <div class="row stats-row">
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total'] ?></div>
                <div class="stat-label">إجمالي الإنجازات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number" style="color: #ffc107;"><?= $stats['new'] ?></div>
                <div class="stat-label">جديد</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number" style="color: #17a2b8;"><?= $stats['under_review'] ?></div>
                <div class="stat-label">قيد المراجعة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745;"><?= $stats['accepted'] ?></div>
                <div class="stat-label">مقبول</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number" style="color: #dc3545;"><?= $stats['rejected'] ?></div>
                <div class="stat-label">مرفوض</div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة إنجاز جديد -->
    <div class="card card-custom">
        <div class="card-header">
            ➕ إضافة إنجاز جديد
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الوحدة/الشعبة</label>
                        <input type="text" class="form-control" name="unit_name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">عنوان الإنجاز</label>
                        <input type="text" class="form-control" name="achievement_title" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ الإنجاز</label>
                        <input type="date" class="form-control" name="achievement_date" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">مقدم البلاغ</label>
                        <input type="text" class="form-control" name="submitted_by">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">وصف الإنجاز</label>
                    <textarea class="form-control" name="achievement_description" rows="3"></textarea>
                </div>
                <button type="submit" name="add_achievement" class="btn btn-primary">
                    ➕ إضافة الإنجاز
                </button>
            </form>
        </div>
    </div>

    <!-- جدول الإنجازات -->
    <div class="card card-custom">
        <div class="card-header">
            📋 قائمة الإنجازات المستلمة
        </div>
        <div class="card-body">
            <?php if (!empty($achievements)): ?>
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>الوحدة/الشعبة</th>
                                <th>عنوان الإنجاز</th>
                                <th>تاريخ الإنجاز</th>
                                <th>مقدم البلاغ</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($achievements as $achievement): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($achievement['unit_name']) ?></strong></td>
                                    <td><?= htmlspecialchars($achievement['achievement_title']) ?></td>
                                    <td><?= date('Y/m/d', strtotime($achievement['achievement_date'])) ?></td>
                                    <td><?= htmlspecialchars($achievement['submitted_by'] ?? 'غير محدد') ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($achievement['status']) {
                                            case 'جديد':
                                                $status_class = 'status-new';
                                                break;
                                            case 'قيد المراجعة':
                                                $status_class = 'status-review';
                                                break;
                                            case 'مقبول':
                                                $status_class = 'status-accepted';
                                                break;
                                            case 'مرفوض':
                                                $status_class = 'status-rejected';
                                                break;
                                        }
                                        ?>
                                        <span class="status-badge <?= $status_class ?>">
                                            <?= htmlspecialchars($achievement['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= date('Y/m/d - H:i', strtotime($achievement['created_at'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showDetails(<?= $achievement['id'] ?>)">
                                            👁️ عرض
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="updateStatus(<?= $achievement['id'] ?>, '<?= $achievement['status'] ?>')">
                                            ✏️ تحديث
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5" style="color: #666;">
                    <div style="font-size: 3em;">📭</div>
                    <h4>لا توجد إنجازات مسجلة حتى الآن</h4>
                    <p>قم بإضافة أول إنجاز باستخدام النموذج أعلاه</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الإنجاز -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: #1E2124; color: white; border: 1px solid #333;">
            <div class="modal-header" style="border-bottom: 1px solid #333;">
                <h5 class="modal-title">تفاصيل الإنجاز</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Modal لتحديث الحالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: #1E2124; color: white; border: 1px solid #333;">
            <div class="modal-header" style="border-bottom: 1px solid #333;">
                <h5 class="modal-title">تحديث حالة الإنجاز</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="achievement_id" id="statusAchievementId">
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="new_status" id="newStatus" required>
                            <option value="جديد">جديد</option>
                            <option value="قيد المراجعة">قيد المراجعة</option>
                            <option value="مقبول">مقبول</option>
                            <option value="مرفوض">مرفوض</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #333;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_status" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
const achievements = <?= json_encode($achievements) ?>;

function showDetails(id) {
    const achievement = achievements.find(a => a.id == id);
    if (achievement) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>الوحدة/الشعبة:</strong><br>
                    ${achievement.unit_name}
                </div>
                <div class="col-md-6">
                    <strong>عنوان الإنجاز:</strong><br>
                    ${achievement.achievement_title}
                </div>
            </div>
            <hr style="border-color: #333;">
            <div class="row">
                <div class="col-md-6">
                    <strong>تاريخ الإنجاز:</strong><br>
                    ${new Date(achievement.achievement_date).toLocaleDateString('ar-EG')}
                </div>
                <div class="col-md-6">
                    <strong>مقدم البلاغ:</strong><br>
                    ${achievement.submitted_by || 'غير محدد'}
                </div>
            </div>
            <hr style="border-color: #333;">
            <div>
                <strong>وصف الإنجاز:</strong><br>
                ${achievement.achievement_description || 'لا يوجد وصف'}
            </div>
            <hr style="border-color: #333;">
            <div class="row">
                <div class="col-md-6">
                    <strong>الحالة:</strong><br>
                    <span class="status-badge ${getStatusClass(achievement.status)}">
                        ${achievement.status}
                    </span>
                </div>
                <div class="col-md-6">
                    <strong>تاريخ الإضافة:</strong><br>
                    ${new Date(achievement.created_at).toLocaleString('ar-EG')}
                </div>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('detailsModal')).show();
    }
}

function updateStatus(id, currentStatus) {
    document.getElementById('statusAchievementId').value = id;
    document.getElementById('newStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function getStatusClass(status) {
    switch (status) {
        case 'جديد': return 'status-new';
        case 'قيد المراجعة': return 'status-review';
        case 'مقبول': return 'status-accepted';
        case 'مرفوض': return 'status-rejected';
        default: return '';
    }
}
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
