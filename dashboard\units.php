<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("إدارة الوحدات - الرصين", false, "../index.php");
include '../config/db.php';

// إنشاء جدول الوحدات إذا لم يكن موجوداً
$table_check = $conn->query("SHOW TABLES LIKE 'units'");
if (!$table_check || $table_check->num_rows == 0) {
    $create_table = "CREATE TABLE IF NOT EXISTS units (
        id INT AUTO_INCREMENT PRIMARY KEY,
        unit_name VARCHAR(100) NOT NULL,
        unit_type ENUM('وحدة', 'شعبة', 'قسم', 'مديرية') DEFAULT 'وحدة',
        commander_name VARCHAR(100),
        contact_phone VARCHAR(20),
        contact_email VARCHAR(100),
        location VARCHAR(200),
        status ENUM('نشط', 'غير نشط', 'مؤقت') DEFAULT 'نشط',
        parent_unit_id INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_unit_id) REFERENCES units(id) ON DELETE SET NULL
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";
    $conn->query($create_table);
}

// معالجة إضافة وحدة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_unit'])) {
    $unit_name = $_POST['unit_name'] ?? '';
    $unit_type = $_POST['unit_type'] ?? 'وحدة';
    $commander_name = $_POST['commander_name'] ?? '';
    $contact_phone = $_POST['contact_phone'] ?? '';
    $contact_email = $_POST['contact_email'] ?? '';
    $location = $_POST['location'] ?? '';
    $parent_unit_id = !empty($_POST['parent_unit_id']) ? $_POST['parent_unit_id'] : null;
    
    if (!empty($unit_name)) {
        $stmt = $conn->prepare("INSERT INTO units (unit_name, unit_type, commander_name, contact_phone, contact_email, location, parent_unit_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("ssssssi", $unit_name, $unit_type, $commander_name, $contact_phone, $contact_email, $location, $parent_unit_id);
            $stmt->execute();
            $stmt->close();
            $success_message = "تم إضافة الوحدة بنجاح!";
        }
    } else {
        $error_message = "يرجى إدخال اسم الوحدة!";
    }
}

// معالجة تحديث حالة الوحدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $unit_id = $_POST['unit_id'] ?? '';
    $new_status = $_POST['new_status'] ?? '';
    
    if (!empty($unit_id) && !empty($new_status)) {
        $stmt = $conn->prepare("UPDATE units SET status = ? WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("si", $new_status, $unit_id);
            $stmt->execute();
            $stmt->close();
            $update_message = "تم تحديث حالة الوحدة بنجاح!";
        }
    }
}

// جلب الوحدات
$units = [];
$result = $conn->query("SELECT u.*, p.unit_name as parent_name FROM units u LEFT JOIN units p ON u.parent_unit_id = p.id ORDER BY u.unit_type, u.unit_name");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $units[] = $row;
    }
}

// جلب الوحدات الرئيسية للاختيار كوحدة أب
$parent_units = [];
$parent_result = $conn->query("SELECT id, unit_name, unit_type FROM units WHERE parent_unit_id IS NULL ORDER BY unit_name");
if ($parent_result) {
    while ($row = $parent_result->fetch_assoc()) {
        $parent_units[] = $row;
    }
}

// إحصائيات الوحدات
$stats = [
    'total' => 0,
    'active' => 0,
    'inactive' => 0,
    'temporary' => 0,
    'units' => 0,
    'departments' => 0
];

$stats_result = $conn->query("SELECT status, unit_type, COUNT(*) as count FROM units GROUP BY status, unit_type");
if ($stats_result) {
    while ($row = $stats_result->fetch_assoc()) {
        $stats['total'] += $row['count'];
        
        // حسب الحالة
        switch ($row['status']) {
            case 'نشط':
                $stats['active'] += $row['count'];
                break;
            case 'غير نشط':
                $stats['inactive'] += $row['count'];
                break;
            case 'مؤقت':
                $stats['temporary'] += $row['count'];
                break;
        }
        
        // حسب النوع
        if ($row['unit_type'] === 'وحدة') {
            $stats['units'] += $row['count'];
        } else {
            $stats['departments'] += $row['count'];
        }
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .page-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
        text-align: center;
    }
    .stats-row {
        margin-bottom: 30px;
    }
    .stat-card {
        background: #1E2124;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        color: white;
        border: 1px solid #333;
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .stat-label {
        font-size: 1.1em;
        opacity: 0.8;
    }
    .card-custom {
        background: #1E2124;
        border: 1px solid #333;
        border-radius: 15px;
        color: white;
        margin-bottom: 20px;
    }
    .card-header {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border-radius: 15px 15px 0 0;
        padding: 15px 20px;
        font-weight: bold;
    }
    .form-control, .form-select {
        background: #2a2d31;
        border: 1px solid #444;
        color: white;
        border-radius: 8px;
    }
    .form-control:focus, .form-select:focus {
        background: #2a2d31;
        border-color: #1DA1F2;
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
    }
    .btn-primary {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border: none;
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: bold;
    }
    .btn-outline-primary {
        color: #1DA1F2;
        border-color: #1DA1F2;
        border-radius: 25px;
        padding: 8px 20px;
    }
    .btn-outline-primary:hover {
        background: #1DA1F2;
        color: white;
    }
    .table-dark {
        background: #1E2124;
        border-radius: 10px;
        overflow: hidden;
    }
    .table-dark th {
        background: #2a2d31;
        border-color: #444;
    }
    .table-dark td {
        border-color: #444;
    }
    .status-badge {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .status-active { background: #28a745; color: #fff; }
    .status-inactive { background: #dc3545; color: #fff; }
    .status-temporary { background: #ffc107; color: #000; }
    .unit-type-badge {
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.75em;
        font-weight: bold;
    }
    .type-unit { background: #17a2b8; color: #fff; }
    .type-department { background: #6f42c1; color: #fff; }
    .type-section { background: #fd7e14; color: #fff; }
    .type-directorate { background: #e83e8c; color: #fff; }
    .alert-success {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid #28a745;
        color: #28a745;
        border-radius: 10px;
    }
    .alert-danger {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid #dc3545;
        color: #dc3545;
        border-radius: 10px;
    }
</style>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="page-title">🏢 إدارة الوحدات والشعب التابعة</h1>
        <a href="/rasseem/dashboard/index.php" class="btn btn-outline-primary">
            🏠 العودة للرئيسية
        </a>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success"><?= $success_message ?></div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger"><?= $error_message ?></div>
    <?php endif; ?>
    
    <?php if (isset($update_message)): ?>
        <div class="alert alert-success"><?= $update_message ?></div>
    <?php endif; ?>

    <!-- إحصائيات الوحدات -->
    <div class="row stats-row">
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total'] ?></div>
                <div class="stat-label">إجمالي الوحدات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745;"><?= $stats['active'] ?></div>
                <div class="stat-label">نشط</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card">
                <div class="stat-number" style="color: #dc3545;"><?= $stats['inactive'] ?></div>
                <div class="stat-label">غير نشط</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number" style="color: #ffc107;"><?= $stats['temporary'] ?></div>
                <div class="stat-label">مؤقت</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number" style="color: #17a2b8;"><?= $stats['units'] ?></div>
                <div class="stat-label">وحدات</div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة وحدة جديدة -->
    <div class="card card-custom">
        <div class="card-header">
            ➕ إضافة وحدة/شعبة جديدة
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الوحدة/الشعبة *</label>
                        <input type="text" class="form-control" name="unit_name" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">نوع الوحدة</label>
                        <select class="form-select" name="unit_type">
                            <option value="وحدة">وحدة</option>
                            <option value="شعبة">شعبة</option>
                            <option value="قسم">قسم</option>
                            <option value="مديرية">مديرية</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم القائد/المسؤول</label>
                        <input type="text" class="form-control" name="commander_name">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" name="contact_phone">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="contact_email">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الوحدة الأب (اختياري)</label>
                        <select class="form-select" name="parent_unit_id">
                            <option value="">-- لا يوجد --</option>
                            <?php foreach ($parent_units as $parent): ?>
                                <option value="<?= $parent['id'] ?>">
                                    <?= htmlspecialchars($parent['unit_name']) ?> (<?= $parent['unit_type'] ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">الموقع</label>
                    <input type="text" class="form-control" name="location" placeholder="مثال: بغداد - الكرخ - المنطقة الخضراء">
                </div>
                <button type="submit" name="add_unit" class="btn btn-primary">
                    ➕ إضافة الوحدة
                </button>
            </form>
        </div>
    </div>

    <!-- جدول الوحدات -->
    <div class="card card-custom">
        <div class="card-header">
            📋 قائمة الوحدات والشعب المسجلة
        </div>
        <div class="card-body">
            <?php if (!empty($units)): ?>
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>اسم الوحدة</th>
                                <th>النوع</th>
                                <th>القائد/المسؤول</th>
                                <th>الهاتف</th>
                                <th>الوحدة الأب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($units as $unit): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($unit['unit_name']) ?></strong></td>
                                    <td>
                                        <?php
                                        $type_class = '';
                                        switch ($unit['unit_type']) {
                                            case 'وحدة':
                                                $type_class = 'type-unit';
                                                break;
                                            case 'شعبة':
                                                $type_class = 'type-department';
                                                break;
                                            case 'قسم':
                                                $type_class = 'type-section';
                                                break;
                                            case 'مديرية':
                                                $type_class = 'type-directorate';
                                                break;
                                        }
                                        ?>
                                        <span class="unit-type-badge <?= $type_class ?>">
                                            <?= htmlspecialchars($unit['unit_type']) ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($unit['commander_name'] ?? 'غير محدد') ?></td>
                                    <td><?= htmlspecialchars($unit['contact_phone'] ?? 'غير محدد') ?></td>
                                    <td><?= htmlspecialchars($unit['parent_name'] ?? 'لا يوجد') ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($unit['status']) {
                                            case 'نشط':
                                                $status_class = 'status-active';
                                                break;
                                            case 'غير نشط':
                                                $status_class = 'status-inactive';
                                                break;
                                            case 'مؤقت':
                                                $status_class = 'status-temporary';
                                                break;
                                        }
                                        ?>
                                        <span class="status-badge <?= $status_class ?>">
                                            <?= htmlspecialchars($unit['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showDetails(<?= $unit['id'] ?>)">
                                            👁️ عرض
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning" onclick="updateStatus(<?= $unit['id'] ?>, '<?= $unit['status'] ?>')">
                                            ✏️ تحديث
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5" style="color: #666;">
                    <div style="font-size: 3em;">🏢</div>
                    <h4>لا توجد وحدات مسجلة حتى الآن</h4>
                    <p>قم بإضافة أول وحدة باستخدام النموذج أعلاه</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الوحدة -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: #1E2124; color: white; border: 1px solid #333;">
            <div class="modal-header" style="border-bottom: 1px solid #333;">
                <h5 class="modal-title">تفاصيل الوحدة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Modal لتحديث الحالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: #1E2124; color: white; border: 1px solid #333;">
            <div class="modal-header" style="border-bottom: 1px solid #333;">
                <h5 class="modal-title">تحديث حالة الوحدة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="unit_id" id="statusUnitId">
                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة</label>
                        <select class="form-select" name="new_status" id="newStatus" required>
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                            <option value="مؤقت">مؤقت</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #333;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_status" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
const units = <?= json_encode($units) ?>;

function showDetails(id) {
    const unit = units.find(u => u.id == id);
    if (unit) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>اسم الوحدة:</strong><br>
                    ${unit.unit_name}
                </div>
                <div class="col-md-6">
                    <strong>نوع الوحدة:</strong><br>
                    <span class="unit-type-badge ${getTypeClass(unit.unit_type)}">
                        ${unit.unit_type}
                    </span>
                </div>
            </div>
            <hr style="border-color: #333;">
            <div class="row">
                <div class="col-md-6">
                    <strong>القائد/المسؤول:</strong><br>
                    ${unit.commander_name || 'غير محدد'}
                </div>
                <div class="col-md-6">
                    <strong>رقم الهاتف:</strong><br>
                    ${unit.contact_phone || 'غير محدد'}
                </div>
            </div>
            <hr style="border-color: #333;">
            <div class="row">
                <div class="col-md-6">
                    <strong>البريد الإلكتروني:</strong><br>
                    ${unit.contact_email || 'غير محدد'}
                </div>
                <div class="col-md-6">
                    <strong>الوحدة الأب:</strong><br>
                    ${unit.parent_name || 'لا يوجد'}
                </div>
            </div>
            <hr style="border-color: #333;">
            <div>
                <strong>الموقع:</strong><br>
                ${unit.location || 'غير محدد'}
            </div>
            <hr style="border-color: #333;">
            <div class="row">
                <div class="col-md-6">
                    <strong>الحالة:</strong><br>
                    <span class="status-badge ${getStatusClass(unit.status)}">
                        ${unit.status}
                    </span>
                </div>
                <div class="col-md-6">
                    <strong>تاريخ الإضافة:</strong><br>
                    ${new Date(unit.created_at).toLocaleString('ar-EG')}
                </div>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('detailsModal')).show();
    }
}

function updateStatus(id, currentStatus) {
    document.getElementById('statusUnitId').value = id;
    document.getElementById('newStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function getStatusClass(status) {
    switch (status) {
        case 'نشط': return 'status-active';
        case 'غير نشط': return 'status-inactive';
        case 'مؤقت': return 'status-temporary';
        default: return '';
    }
}

function getTypeClass(type) {
    switch (type) {
        case 'وحدة': return 'type-unit';
        case 'شعبة': return 'type-department';
        case 'قسم': return 'type-section';
        case 'مديرية': return 'type-directorate';
        default: return '';
    }
}
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
