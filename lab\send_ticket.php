<?php
session_start();
include '../includes/auth_lab.php';
include '../config/db.php';

$success_message = '';
$error_message = '';

// معالجة إرسال التذكرة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $priority = $_POST['priority'] ?? 'متوسط';
    $category = $_POST['category'] ?? 'عام';

    // التحقق من صحة البيانات
    if (empty($title) || empty($description)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // التحقق من وجود جدول التذاكر وإنشاؤه إذا لم يكن موجوداً
            $table_check = $conn->query("SHOW TABLES LIKE 'tickets'");
            if (!$table_check || $table_check->num_rows == 0) {
                $create_table = "CREATE TABLE IF NOT EXISTS tickets (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT NOT NULL,
                    sender_department VARCHAR(100) DEFAULT 'المخابر',
                    sender_name VARCHAR(100),
                    priority ENUM('منخفض', 'متوسط', 'عالي', 'عاجل') DEFAULT 'متوسط',
                    category VARCHAR(100) DEFAULT 'عام',
                    status ENUM('مفتوحة', 'قيد المعالجة', 'مغلقة') DEFAULT 'مفتوحة',
                    attachment_path VARCHAR(500),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

                if (!$conn->query($create_table)) {
                    throw new Exception("خطأ في إنشاء جدول التذاكر: " . $conn->error);
                }
            }

            // معالجة رفع الملف
            $attachment_path = null;
            if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/tickets/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = pathinfo($_FILES['attachment']['name'], PATHINFO_EXTENSION);
                $file_name = 'ticket_' . time() . '_' . uniqid() . '.' . $file_extension;
                $attachment_path = $upload_dir . $file_name;

                if (!move_uploaded_file($_FILES['attachment']['tmp_name'], $attachment_path)) {
                    throw new Exception("خطأ في رفع الملف");
                }
            }

            // إدراج التذكرة في قاعدة البيانات
            $sender_name = $_SESSION['user']['ar_name'] ?? 'مستخدم المخابر';
            $stmt = $conn->prepare("INSERT INTO tickets (title, description, sender_department, sender_name, priority, category, attachment_path) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("sssssss", $title, $description, 'المخابر', $sender_name, $priority, $category, $attachment_path);

            if ($stmt->execute()) {
                $ticket_id = $conn->insert_id;
                $success_message = "تم إرسال التذكرة بنجاح! رقم التذكرة: #$ticket_id";

                // إعادة تعيين المتغيرات
                $title = $description = '';
            } else {
                throw new Exception("خطأ في حفظ التذكرة: " . $stmt->error);
            }

            $stmt->close();
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

include '../includes/header.php';
renderHeader("إرسال تذكرة - المخابر", true, "index.php");
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .ticket-form {
        background: #1E2124;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        max-width: 800px;
        margin: 0 auto;
    }
    .form-control {
        background: #2a2d31 !important;
        border: 1px solid #444;
        color: #fff !important;
        border-radius: 10px;
    }
    .form-control:focus {
        background: #2a2d31 !important;
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
        color: #fff !important;
    }
    .form-label {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .btn-submit {
        background: linear-gradient(135deg, #1DA1F2, #0d7ec7);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 30px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(29, 161, 242, 0.4);
    }
    .alert {
        border-radius: 10px;
        border: none;
    }
    .priority-high { color: #dc3545; }
    .priority-medium { color: #ffc107; }
    .priority-low { color: #28a745; }
</style>

<div class="container mt-4">
    <?php if ($success_message): ?>
        <div class="alert alert-success text-center">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger text-center">
            <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
        </div>
    <?php endif; ?>

    <div class="ticket-form">
        <h4 class="mb-4 text-center" style="color: #1DA1F2;">📩 إرسال تذكرة دعم فني</h4>

        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="title" class="form-label">* عنوان المشكلة</label>
                        <input type="text" class="form-control" id="title" name="title"
                               value="<?= htmlspecialchars($title ?? '') ?>"
                               placeholder="مثال: عطل في جهاز الاتصال الرئيسي" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="priority" class="form-label">الأولوية</label>
                        <select class="form-control" id="priority" name="priority">
                            <option value="منخفض">🟢 منخفض</option>
                            <option value="متوسط" selected>🟡 متوسط</option>
                            <option value="عالي">🟠 عالي</option>
                            <option value="عاجل">🔴 عاجل</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="category" class="form-label">فئة المشكلة</label>
                <select class="form-control" id="category" name="category">
                    <option value="عام">عام</option>
                    <option value="أجهزة">أجهزة</option>
                    <option value="برمجيات">برمجيات</option>
                    <option value="شبكة">شبكة</option>
                    <option value="صيانة">صيانة</option>
                    <option value="تدريب">تدريب</option>
                </select>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">* وصف تفصيلي للمشكلة</label>
                <textarea class="form-control" id="description" name="description" rows="5"
                          placeholder="اكتب وصفًا مفصلًا للمشكلة، متى حدثت، والخطوات التي اتخذتها..." required><?= htmlspecialchars($description ?? '') ?></textarea>
            </div>

            <div class="mb-4">
                <label for="attachment" class="form-label">مرفق (صورة أو ملف - اختياري)</label>
                <input type="file" class="form-control" id="attachment" name="attachment"
                       accept="image/*,.pdf,.doc,.docx,.txt">
                <small class="text-muted">الحد الأقصى: 5MB - الأنواع المدعومة: صور، PDF، Word، نص</small>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-submit btn-lg">
                    <i class="fas fa-paper-plane"></i> إرسال التذكرة
                </button>
                <a href="index.php" class="btn btn-outline-secondary btn-lg ms-3">
                    <i class="fas fa-arrow-right"></i> العودة
                </a>
            </div>
        </form>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>