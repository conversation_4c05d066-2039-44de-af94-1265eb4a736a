<?php
session_start();
include '../includes/auth_lab.php';
include '../config/db.php';

$success_message = '';
$error_message = '';

// معالجة إرسال التذكرة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $priority = $_POST['priority'] ?? 'متوسط';
    $category = $_POST['category'] ?? 'عام';

    // التحقق من صحة البيانات
    if (empty($title) || empty($description)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // التحقق من وجود جدول التذاكر وإنشاؤه إذا لم يكن موجوداً
            $table_check = $conn->query("SHOW TABLES LIKE 'tickets'");
            if (!$table_check || $table_check->num_rows == 0) {
                $create_table = "CREATE TABLE IF NOT EXISTS tickets (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT NOT NULL,
                    sender_department VARCHAR(100) DEFAULT 'المخابر',
                    sender_name VARCHAR(100),
                    priority ENUM('منخفض', 'متوسط', 'عالي', 'عاجل') DEFAULT 'متوسط',
                    category VARCHAR(100) DEFAULT 'عام',
                    status ENUM('مفتوحة', 'قيد المعالجة', 'مغلقة') DEFAULT 'مفتوحة',
                    attachment_path VARCHAR(500),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";

                if (!$conn->query($create_table)) {
                    throw new Exception("خطأ في إنشاء جدول التذاكر: " . $conn->error);
                }
            }

            // معالجة رفع الملف
            $attachment_path = null;
            if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/tickets/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = pathinfo($_FILES['attachment']['name'], PATHINFO_EXTENSION);
                $file_name = 'ticket_' . time() . '_' . uniqid() . '.' . $file_extension;
                $attachment_path = $upload_dir . $file_name;

                if (!move_uploaded_file($_FILES['attachment']['tmp_name'], $attachment_path)) {
                    throw new Exception("خطأ في رفع الملف");
                }
            }

            // إدراج التذكرة في قاعدة البيانات
            $sender_name = $_SESSION['user']['ar_name'] ?? 'مستخدم المخابر';
            $stmt = $conn->prepare("INSERT INTO tickets (title, description, sender_department, sender_name, priority, category, attachment_path) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("sssssss", $title, $description, 'المخابر', $sender_name, $priority, $category, $attachment_path);

            if ($stmt->execute()) {
                $ticket_id = $conn->insert_id;
                $success_message = "تم إرسال التذكرة بنجاح! رقم التذكرة: #$ticket_id";

                // إعادة تعيين المتغيرات
                $title = $description = '';
            } else {
                throw new Exception("خطأ في حفظ التذكرة: " . $stmt->error);
            }

            $stmt->close();
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

include '../includes/header.php';
renderHeader("إرسال تذكرة - المخابر", true, "index.php");
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .ticket-card {
        max-width: 500px;
        margin: 50px auto 0 auto;
        background: #23272b;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        padding: 35px 30px 30px 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }
    .ticket-card:before {
        content: "";
        position: absolute;
        top: -60px;
        right: -60px;
        width: 180px;
        height: 180px;
        background: linear-gradient(135deg, #1DA1F2 0%, #0d6efd 100%);
        opacity: 0.15;
        border-radius: 50%;
        z-index: 0;
    }
    .ticket-card h2 {
        font-weight: bold;
        color: #1DA1F2;
        margin-bottom: 25px;
        z-index: 1;
        position: relative;
    }
    .ticket-card label {
        color: #b5b5b5;
        font-weight: 500;
    }
    .ticket-card .form-control,
    .ticket-card .form-select {
        background: #181a1b !important;
        color: #fff !important;
        border: 1px solid #222;
        border-radius: 8px;
    }
    .ticket-card .form-control:focus,
    .ticket-card .form-select:focus {
        background: #181a1b !important;
        color: #fff !important;
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29,161,242,.25);
    }
    .ticket-card .form-control::placeholder {
        color: #666 !important;
    }
    .ticket-card .btn-success {
        background: linear-gradient(90deg, #1DA1F2 0%, #0d6efd 100%);
        border: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .ticket-card .btn-success:hover {
        background: linear-gradient(90deg, #0d6efd 0%, #1DA1F2 100%);
        transform: scale(1.04);
    }
    .ticket-card .icon {
        font-size: 2.5rem;
        color: #1DA1F2;
        margin-bottom: 10px;
        display: block;
        text-align: center;
    }
    .alert {
        max-width: 500px;
        margin: 20px auto;
        border-radius: 10px;
        border: none;
    }
    .ticket-card .btn-outline-secondary {
        color: #b5b5b5;
        border-color: #444;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s;
    }
    .ticket-card .btn-outline-secondary:hover {
        background-color: #444;
        border-color: #666;
        color: #fff;
    }
</style>

<?php if ($success_message): ?>
    <div class="alert alert-success text-center">
        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger text-center">
        <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
    </div>
<?php endif; ?>

<div class="ticket-card">
    <div class="icon">📩</div>
    <h2 class="text-center mb-4">إرسال تذكرة دعم فني</h2>
    <form method="POST" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="attachment" class="form-label">إرفاق صورة (اختياري)</label>
            <input type="file" class="form-control" id="attachment" name="attachment" accept="image/*,.pdf,.doc,.docx,.txt">
        </div>
        <div class="mb-3">
            <label for="title" class="form-label">عنوان المشكلة</label>
            <input type="text" class="form-control" id="title" name="title"
                   value="<?= htmlspecialchars($title ?? '') ?>"
                   placeholder="مثال: عطل في جهاز الاتصال الرئيسي" required>
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">وصف المشكلة</label>
            <textarea class="form-control" id="description" name="description" required rows="4"
                      placeholder="اكتب وصفًا مفصلًا للمشكلة..."><?= htmlspecialchars($description ?? '') ?></textarea>
        </div>
        <div class="mb-3">
            <label for="priority" class="form-label">الأولوية</label>
            <select class="form-select" id="priority" name="priority" required>
                <option value="منخفض">🟢 منخفض</option>
                <option value="متوسط" selected>🟡 متوسط</option>
                <option value="عالي">🟠 عالي</option>
                <option value="عاجل">🔴 عاجل</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="category" class="form-label">فئة المشكلة</label>
            <select class="form-select" id="category" name="category">
                <option value="عام">عام</option>
                <option value="أجهزة">أجهزة</option>
                <option value="برمجيات">برمجيات</option>
                <option value="شبكة">شبكة</option>
                <option value="صيانة">صيانة</option>
                <option value="تدريب">تدريب</option>
            </select>
        </div>
        <input type="hidden" name="sender_department" value="المخابر">
        <button type="submit" class="btn btn-success w-100 mb-3">حفظ التذكرة</button>
        <div class="text-center">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i> العودة للرئيسية
            </a>
        </div>
    </form>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>