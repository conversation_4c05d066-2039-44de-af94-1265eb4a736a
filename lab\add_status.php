<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("إضافة موقف اتصالات المركزية", true, "index.php");

// معالجة الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'] ?? [];
    $notes = $_POST['note'] ?? [];
    $_SESSION['central_status_result'] = [
        'devices' => $devices,
        'statuses' => $statuses,
        'notes' => $notes,
        'time' => date('Y-m-d H:i:s')
    ];
    header('Location: confirm_send.php');
    exit;
}
?>

<style>
        body {
            background: linear-gradient(135deg, #232526 0%, #1e3c72 100%);
            min-height: 100vh;
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
        }
        .central-status-section {
            background: rgba(255,255,255,0.07);
            border-radius: 22px;
            box-shadow: 0 8px 32px 0 rgba(30,60,114,0.18);
            padding: 38px 22px 28px 22px;
            margin: 48px auto 0 auto;
            max-width: 850px;
            border: 1.5px solid #1da1f2;
            backdrop-filter: blur(4px);
        }
        .central-status-section h4 {
            color: #1DA1F2;
            font-weight: bold;
            margin-bottom: 32px;
            letter-spacing: 1.5px;
            font-size: 2em;
        }
        .central-table {
            background: rgba(255,255,255,0.04);
            border-radius: 14px;
            overflow: hidden;
            box-shadow: 0 2px 16px rgba(29,161,242,0.10);
        }
        .central-table th {
            background: #1da1f2;
            color: #fff;
            font-size: 1.15em;
            font-weight: bold;
            border-bottom: 2px solid #fff;
        }
        .central-table td {
            color: #232526;
            font-size: 1.08em;
            background: rgba(255,255,255,0.13);
        }
        .central-table tr:nth-child(even) td {
            background: rgba(255,255,255,0.18);
        }
        .form-check-label {
            margin: 0 10px 0 2px;
            font-size: 1.12em;
        }
        .form-check-input {
            accent-color: #1DA1F2;
            transform: scale(1.25);
        }
        .btn-central {
            background: linear-gradient(90deg, #1DA1F2 0%, #28a745 100%);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 1.18em;
            font-weight: bold;
            margin-top: 32px;
            padding: 14px 0;
            box-shadow: 0 4px 16px rgba(29,161,242,0.18);
            transition: background 0.3s;
        }
        .btn-central:hover {
            background: linear-gradient(90deg, #28a745 0%, #1DA1F2 100%);
            color: #fff;
        }
    </style>

<div class="central-status-section">
    <h4 class="text-center">📋 موقف اتصالات المركزية</h4>
    <form method="POST" action="")
        <table class="table central-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الجهاز</th>
                    <th>الحالة</th>
                    <th>ملاحظة</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
                foreach ($devices as $index => $device) {
                    echo "<tr>";
                    echo "<td>" . ($index + 1) . "</td>";
                    echo "<td>" . htmlspecialchars($device) . "</td>";
                    echo "<td>";
                    echo "<div class='form-check form-check-inline'>";
                    echo "<input class='form-check-input' type='radio' name='status[" . htmlspecialchars($device) . "]' value='تم الرد' required>";
                    echo "<label class='form-check-label'>تم الرد</label>";
                    echo "</div>";
                    echo "<div class='form-check form-check-inline'>";
                    echo "<input class='form-check-input' type='radio' name='status[" . htmlspecialchars($device) . "]' value='لا يوجد رد' required>";
                    echo "<label class='form-check-label'>لا يوجد رد</label>";
                    echo "</div>";
                    echo "</td>";
                    echo "<td>";
                    echo "<input type='text' class='form-control' name='note[" . htmlspecialchars($device) . "]' placeholder='ملاحظة...'>";
                    echo "</td>";
                    echo "</tr>";
                }
                ?>
            </tbody>
        </table>
        <button type="submit" class="btn btn-central w-100">إرسال</button>
    </form>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>