<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("إضافة موقف اتصالات المركزية", true, "index.php");

// معالجة الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'] ?? [];
    $notes = $_POST['note'] ?? [];
    $_SESSION['central_status_result'] = [
        'devices' => $devices,
        'statuses' => $statuses,
        'notes' => $notes,
        'time' => date('Y-m-d H:i:s')
    ];
    header('Location: confirm_send.php');
    exit;
}
?>

<style>
        body {
            background: linear-gradient(135deg, #232526 0%, #1e3c72 100%);
            min-height: 100vh;
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
        }
        .central-status-section {
            background: rgba(255,255,255,0.95);
            border-radius: 22px;
            box-shadow: 0 12px 40px 0 rgba(30,60,114,0.15);
            padding: 40px 30px 35px 30px;
            margin: 48px auto 0 auto;
            max-width: 1000px;
            border: 2px solid rgba(29,161,242,0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        .central-status-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1da1f2 0%, #28a745 50%, #1da1f2 100%);
        }
        .central-status-section h4 {
            color: #1DA1F2;
            font-weight: bold;
            margin-bottom: 32px;
            letter-spacing: 1.5px;
            font-size: 2em;
        }
        .table-container {
            overflow-x: auto;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(29,161,242,0.15);
        }
        .central-table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            margin: 0;
        }
        .central-table thead th {
            background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
            color: #ffffff;
            font-size: 1.1em;
            font-weight: 700;
            padding: 20px 15px;
            text-align: center;
            border: none;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            white-space: nowrap;
        }
        .central-table tbody td {
            padding: 18px 15px;
            text-align: center;
            border-bottom: 1px solid #e8f4fd;
            vertical-align: middle;
            background: #ffffff;
        }
        .central-table tbody tr:nth-child(even) td {
            background: #f8fcff;
        }
        .central-table tbody tr:hover td {
            background: #e8f4fd;
        }
        .row-number {
            font-weight: 700;
            color: #1da1f2;
            font-size: 1.1em;
            width: 80px;
        }
        .device-name {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.2em;
            width: 120px;
        }
        .status-cell {
            width: 280px;
        }
        .status-options {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .radio-option input[type="radio"] {
            width: 20px;
            height: 20px;
            accent-color: #1da1f2;
            cursor: pointer;
        }
        .radio-option label {
            font-size: 1em;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            white-space: nowrap;
        }
        .radio-option.success label {
            color: #28a745;
        }
        .radio-option.danger label {
            color: #dc3545;
        }
        .note-cell {
            width: 200px;
        }
        .note-input {
            width: 100%;
            max-width: 180px;
            padding: 10px 12px;
            border: 2px solid #e8f4fd;
            border-radius: 8px;
            font-size: 0.95em;
            transition: all 0.3s ease;
            background: #ffffff;
        }
        .note-input:focus {
            outline: none;
            border-color: #1da1f2;
            box-shadow: 0 0 0 3px rgba(29,161,242,0.1);
        }
        .btn-central {
            background: linear-gradient(90deg, #1DA1F2 0%, #28a745 100%);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 1.18em;
            font-weight: bold;
            margin-top: 32px;
            padding: 14px 0;
            box-shadow: 0 4px 16px rgba(29,161,242,0.18);
            transition: background 0.3s;
        }
        .btn-central:hover {
            background: linear-gradient(90deg, #28a745 0%, #1DA1F2 100%);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(29,161,242,0.25);
        }

        /* التوافق مع المتصفحات القديمة */
        @supports not (backdrop-filter: blur(10px)) {
            .central-status-section {
                background: rgba(255,255,255,0.98);
            }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .central-status-section {
                margin: 20px 15px 0 15px;
                padding: 25px 15px 20px 15px;
                max-width: none;
            }
            .central-table thead th,
            .central-table tbody td {
                padding: 12px 8px;
                font-size: 0.9em;
            }
            .status-options {
                flex-direction: column;
                gap: 8px;
            }
            .note-input {
                max-width: 140px;
                font-size: 0.85em;
            }
            .central-status-section h4 {
                font-size: 1.6em;
            }
            .row-number, .device-name {
                font-size: 1em;
            }
        }

        @media (max-width: 480px) {
            .table-container {
                overflow-x: auto;
            }
            .central-table {
                min-width: 600px;
            }
            .central-table thead th,
            .central-table tbody td {
                padding: 10px 6px;
                font-size: 0.85em;
            }
            .status-options {
                gap: 5px;
            }
            .radio-option label {
                font-size: 0.8em;
            }
            .note-input {
                max-width: 100px;
                padding: 6px 8px;
                font-size: 0.8em;
            }
        }

        /* تحسينات إضافية للأداء */
        * {
            box-sizing: border-box;
        }

        .central-table,
        .central-table * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>

<div class="central-status-section">
    <h4 class="text-center">📋 موقف اتصالات المركزية</h4>
    <form method="POST" action="">
        <div class="table-container">
            <table class="central-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الجهاز</th>
                        <th>حالة الاتصال</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
                    foreach ($devices as $index => $device) {
                        echo "<tr>";
                        echo "<td class='row-number'>" . ($index + 1) . "</td>";
                        echo "<td class='device-name'>" . htmlspecialchars($device) . "</td>";
                        echo "<td class='status-cell'>";
                        echo "<div class='status-options'>";
                        echo "<div class='radio-option success'>";
                        echo "<input type='radio' name='status[" . htmlspecialchars($device) . "]' value='تم الرد' id='responded_" . $index . "' required>";
                        echo "<label for='responded_" . $index . "'>✅ تم الرد</label>";
                        echo "</div>";
                        echo "<div class='radio-option danger'>";
                        echo "<input type='radio' name='status[" . htmlspecialchars($device) . "]' value='لا يوجد رد' id='no_response_" . $index . "' required>";
                        echo "<label for='no_response_" . $index . "'>❌ لا يوجد رد</label>";
                        echo "</div>";
                        echo "</div>";
                        echo "</td>";
                        echo "<td class='note-cell'>";
                        echo "<input type='text' class='note-input' name='note[" . htmlspecialchars($device) . "]' placeholder='ملاحظة...'>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
        <button type="submit" class="btn btn-central w-100">إرسال</button>
    </form>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>