<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("إضافة موقف اتصالات المركزية", true, "index.php");

// معالجة الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'] ?? [];
    $notes = $_POST['note'] ?? [];
    $_SESSION['central_status_result'] = [
        'devices' => $devices,
        'statuses' => $statuses,
        'notes' => $notes,
        'time' => date('Y-m-d H:i:s')
    ];
    header('Location: confirm_send.php');
    exit;
}
?>

<style>
        body {
            background: linear-gradient(135deg, #232526 0%, #1e3c72 100%);
            min-height: 100vh;
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
        }
        .central-status-section {
            background: rgba(255,255,255,0.95);
            border-radius: 22px;
            box-shadow: 0 12px 40px 0 rgba(30,60,114,0.15);
            padding: 40px 30px 35px 30px;
            margin: 48px auto 0 auto;
            max-width: 1000px;
            border: 2px solid rgba(29,161,242,0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        .central-status-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1da1f2 0%, #28a745 50%, #1da1f2 100%);
        }
        .central-status-section h4 {
            color: #1DA1F2;
            font-weight: bold;
            margin-bottom: 32px;
            letter-spacing: 1.5px;
            font-size: 2em;
        }
        .table-container {
            overflow-x: auto;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(29,161,242,0.15);
        }
        .central-table {
            width: 100%;
            border-collapse: collapse;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            margin: 0;
        }
        .central-table thead th {
            background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
            color: #ffffff;
            font-size: 1.1em;
            font-weight: 700;
            padding: 20px 15px;
            text-align: center;
            border: none;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            white-space: nowrap;
        }
        .central-table tbody td {
            padding: 18px 15px;
            text-align: center;
            border-bottom: 1px solid #e8f4fd;
            vertical-align: middle;
            background: #ffffff;
        }
        .central-table tbody tr:nth-child(even) td {
            background: #f8fcff;
        }
        .central-table tbody tr:hover td {
            background: #e8f4fd;
        }
        .row-number {
            font-weight: 700;
            color: #1da1f2;
            font-size: 1.1em;
            width: 80px;
        }
        .device-name {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.2em;
            width: 120px;
        }
        .status-cell {
            width: 280px;
        }
        .status-options {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .radio-option input[type="radio"] {
            width: 20px;
            height: 20px;
            accent-color: #1da1f2;
            cursor: pointer;
        }
        .radio-option label {
            font-size: 1em;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            white-space: nowrap;
        }
        .radio-option.success label {
            color: #28a745;
        }
        .radio-option.danger label {
            color: #dc3545;
        }
        .note-cell {
            width: 200px;
        }
        .note-input {
            width: 100%;
            max-width: 180px;
            padding: 10px 12px;
            border: 2px solid #e8f4fd;
            border-radius: 8px;
            font-size: 0.95em;
            transition: all 0.3s ease;
            background: #ffffff;
        }
        .note-input:focus {
            outline: none;
            border-color: #1da1f2;
            box-shadow: 0 0 0 3px rgba(29,161,242,0.1);
        }
        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 32px;
        }
        .btn-preview {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1.18em;
            font-weight: bold;
            padding: 14px 25px;
            box-shadow: 0 4px 16px rgba(111,66,193,0.18);
            transition: all 0.3s ease;
            flex: 1;
        }
        .btn-preview:hover {
            background: linear-gradient(135deg, #e83e8c 0%, #6f42c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(111,66,193,0.25);
        }
        .btn-central {
            background: linear-gradient(90deg, #1DA1F2 0%, #28a745 100%);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 1.18em;
            font-weight: bold;
            padding: 14px 25px;
            box-shadow: 0 4px 16px rgba(29,161,242,0.18);
            transition: all 0.3s ease;
            flex: 1;
        }
        .btn-central:hover {
            background: linear-gradient(90deg, #28a745 0%, #1DA1F2 100%);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(29,161,242,0.25);
        }

        /* التوافق مع المتصفحات القديمة */
        @supports not (backdrop-filter: blur(10px)) {
            .central-status-section {
                background: rgba(255,255,255,0.98);
            }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .central-status-section {
                margin: 20px 15px 0 15px;
                padding: 25px 15px 20px 15px;
                max-width: none;
            }
            .central-table thead th,
            .central-table tbody td {
                padding: 12px 8px;
                font-size: 0.9em;
            }
            .status-options {
                flex-direction: column;
                gap: 8px;
            }
            .note-input {
                max-width: 140px;
                font-size: 0.85em;
            }
            .central-status-section h4 {
                font-size: 1.6em;
            }
            .row-number, .device-name {
                font-size: 1em;
            }
            .form-actions {
                flex-direction: column;
            }
            .preview-modal-content {
                width: 98%;
                margin: 1% auto;
            }
            .preview-modal-header {
                padding: 20px;
            }
            .preview-modal-title {
                font-size: 1.3em;
            }
            .preview-modal-body {
                padding: 20px;
            }
            .preview-device-card {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .preview-device-info {
                justify-content: center;
            }
            .preview-device-actions {
                justify-content: center;
            }
            .preview-modal-footer {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            .preview-actions {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .table-container {
                overflow-x: auto;
            }
            .central-table {
                min-width: 600px;
            }
            .central-table thead th,
            .central-table tbody td {
                padding: 10px 6px;
                font-size: 0.85em;
            }
            .status-options {
                gap: 5px;
            }
            .radio-option label {
                font-size: 0.8em;
            }
            .note-input {
                max-width: 100px;
                padding: 6px 8px;
                font-size: 0.8em;
            }
        }

        /* نافذة المعاينة */
        .preview-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.6);
            backdrop-filter: blur(8px);
        }
        .preview-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 20px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 80px rgba(0,0,0,0.4);
            animation: modalSlideIn 0.4s ease-out;
        }
        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-50px) scale(0.9); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .preview-modal-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 25px 35px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .preview-modal-title {
            font-size: 1.6em;
            font-weight: 700;
            margin: 0;
        }
        .close-preview {
            background: none;
            border: none;
            color: white;
            font-size: 2.2em;
            cursor: pointer;
            padding: 0;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .close-preview:hover {
            background: rgba(255,255,255,0.2);
            transform: rotate(90deg);
        }
        .preview-modal-body {
            padding: 35px;
        }
        .preview-content {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }
        .preview-report-header {
            background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
            color: white;
            padding: 20px 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
        }
        .preview-report-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .preview-report-time {
            font-size: 1em;
            opacity: 0.9;
        }
        .preview-devices-grid {
            display: grid;
            gap: 15px;
        }
        .preview-device-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
        }
        .preview-device-card:hover {
            border-color: #1da1f2;
            box-shadow: 0 4px 15px rgba(29,161,242,0.1);
        }
        .preview-device-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .preview-device-number {
            background: #1da1f2;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
        }
        .preview-device-name {
            font-size: 1.2em;
            font-weight: 700;
            color: #2c3e50;
        }
        .preview-device-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        .preview-device-status.success {
            color: #28a745;
        }
        .preview-device-status.danger {
            color: #dc3545;
        }
        .preview-device-note {
            font-size: 0.9em;
            color: #666;
            font-style: italic;
            margin-top: 5px;
        }
        .preview-device-actions {
            display: flex;
            gap: 8px;
        }
        .btn-edit-device {
            background: #ffc107;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-edit-device:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }
        .btn-delete-device {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-delete-device:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        .preview-modal-footer {
            padding: 25px 35px;
            border-top: 2px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
        }
        .preview-actions {
            display: flex;
            gap: 15px;
        }
        .btn-save {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        }
        .btn-print {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(23,162,184,0.3);
        }
        .btn-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        }
        .device-deleted {
            opacity: 0.5;
            background: #f8d7da !important;
            border-color: #f5c6cb !important;
        }
        .device-deleted .preview-device-name {
            text-decoration: line-through;
        }

        /* تحسينات إضافية للأداء */
        * {
            box-sizing: border-box;
        }

        .central-table,
        .central-table * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>

<div class="central-status-section">
    <h4 class="text-center">📋 موقف اتصالات المركزية</h4>
    <form method="POST" action="">
        <div class="table-container">
            <table class="central-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الجهاز</th>
                        <th>حالة الاتصال</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
                    foreach ($devices as $index => $device) {
                        echo "<tr>";
                        echo "<td class='row-number'>" . ($index + 1) . "</td>";
                        echo "<td class='device-name'>" . htmlspecialchars($device) . "</td>";
                        echo "<td class='status-cell'>";
                        echo "<div class='status-options'>";
                        echo "<div class='radio-option success'>";
                        echo "<input type='radio' name='status[" . htmlspecialchars($device) . "]' value='تم الرد' id='responded_" . $index . "' required>";
                        echo "<label for='responded_" . $index . "'>✅ تم الرد</label>";
                        echo "</div>";
                        echo "<div class='radio-option danger'>";
                        echo "<input type='radio' name='status[" . htmlspecialchars($device) . "]' value='لا يوجد رد' id='no_response_" . $index . "' required>";
                        echo "<label for='no_response_" . $index . "'>❌ لا يوجد رد</label>";
                        echo "</div>";
                        echo "</div>";
                        echo "</td>";
                        echo "<td class='note-cell'>";
                        echo "<input type='text' class='note-input' name='note[" . htmlspecialchars($device) . "]' placeholder='ملاحظة...'>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
        <div class="form-actions">
            <button type="button" class="btn btn-preview" onclick="previewReport()">👁️ معاينة التقرير</button>
            <button type="submit" class="btn btn-central">إرسال</button>
        </div>
    </form>
</div>

<!-- نافذة المعاينة والتعديل -->
<div id="previewModal" class="preview-modal">
    <div class="preview-modal-content">
        <div class="preview-modal-header">
            <h2 class="preview-modal-title">👁️ معاينة وتعديل التقرير</h2>
            <button class="close-preview" onclick="closePreviewModal()">&times;</button>
        </div>
        <div class="preview-modal-body">
            <div id="previewContent" class="preview-content">
                <!-- سيتم إدراج المحتوى هنا بواسطة JavaScript -->
            </div>
        </div>
        <div class="preview-modal-footer">
            <div class="preview-info">
                <span>💡 يمكنك تعديل أو حذف الأجهزة قبل الإرسال</span>
            </div>
            <div class="preview-actions">
                <button class="btn-cancel" onclick="closePreviewModal()">إلغاء</button>
                <button class="btn-print" onclick="printPreview()">🖨️ طباعة</button>
                <button class="btn-save" onclick="saveAndSubmit()">💾 حفظ وإرسال</button>
            </div>
        </div>
    </div>
</div>

<script>
let previewData = [];
let deletedDevices = new Set();

// معاينة التقرير
function previewReport() {
    const form = document.querySelector('form');
    const formData = new FormData(form);

    // التحقق من اكتمال البيانات
    const devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    let hasErrors = false;
    let errorMessage = '';

    devices.forEach(device => {
        const status = formData.get(`status[${device}]`);
        if (!status) {
            hasErrors = true;
            errorMessage += `يرجى تحديد حالة الجهاز: ${device}\n`;
        }
    });

    if (hasErrors) {
        alert(errorMessage);
        return;
    }

    // إنشاء بيانات المعاينة
    previewData = [];
    devices.forEach((device, index) => {
        const status = formData.get(`status[${device}]`);
        const note = formData.get(`note[${device}]`) || '';

        previewData.push({
            index: index,
            device: device,
            status: status,
            note: note,
            deleted: false
        });
    });

    // إنشاء محتوى المعاينة
    generatePreviewContent();

    // إظهار النافذة
    document.getElementById('previewModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// إنشاء محتوى المعاينة
function generatePreviewContent() {
    const container = document.getElementById('previewContent');
    const currentTime = new Date().toLocaleString('ar-SA');

    let content = `
        <div class="preview-report-header">
            <div class="preview-report-title">📋 موقف اتصالات المركزية</div>
            <div class="preview-report-time">📅 ${currentTime}</div>
        </div>
        <div class="preview-devices-grid">
    `;

    previewData.forEach((item, index) => {
        if (deletedDevices.has(item.device)) return;

        const statusClass = item.status === 'تم الرد' ? 'success' : 'danger';
        const statusIcon = item.status === 'تم الرد' ? '✅' : '❌';
        const noteDisplay = item.note ? `<div class="preview-device-note">📝 ${item.note}</div>` : '';

        content += `
            <div class="preview-device-card" data-device="${item.device}">
                <div class="preview-device-info">
                    <div class="preview-device-number">${index + 1}</div>
                    <div>
                        <div class="preview-device-name">${item.device}</div>
                        <div class="preview-device-status ${statusClass}">
                            ${statusIcon} ${item.status}
                        </div>
                        ${noteDisplay}
                    </div>
                </div>
                <div class="preview-device-actions">
                    <button class="btn-edit-device" onclick="editDevice('${item.device}')">
                        ✏️ تعديل
                    </button>
                    <button class="btn-delete-device" onclick="deleteDevice('${item.device}')">
                        🗑️ حذف
                    </button>
                </div>
            </div>
        `;
    });

    content += `</div>`;
    container.innerHTML = content;
}

// تعديل جهاز
function editDevice(deviceName) {
    const deviceData = previewData.find(item => item.device === deviceName);
    if (!deviceData) return;

    const newStatus = prompt(
        `تعديل حالة الجهاز: ${deviceName}\n\nاختر:\n1 - تم الرد\n2 - لا يوجد رد\n\nأدخل 1 أو 2:`,
        deviceData.status === 'تم الرد' ? '1' : '2'
    );

    if (newStatus === null) return;

    let statusText = '';
    if (newStatus === '1') {
        statusText = 'تم الرد';
    } else if (newStatus === '2') {
        statusText = 'لا يوجد رد';
    } else {
        alert('يرجى إدخال 1 أو 2 فقط');
        return;
    }

    const newNote = prompt(
        `تعديل ملاحظة الجهاز: ${deviceName}`,
        deviceData.note
    );

    if (newNote === null) return;

    // تحديث البيانات
    deviceData.status = statusText;
    deviceData.note = newNote;

    // إعادة إنشاء المحتوى
    generatePreviewContent();

    // إظهار رسالة نجاح
    showNotification(`تم تعديل بيانات الجهاز: ${deviceName}`, 'success');
}

// حذف جهاز
function deleteDevice(deviceName) {
    if (confirm(`هل أنت متأكد من حذف الجهاز: ${deviceName}؟`)) {
        deletedDevices.add(deviceName);

        // إعادة إنشاء المحتوى
        generatePreviewContent();

        // إظهار رسالة نجاح
        showNotification(`تم حذف الجهاز: ${deviceName}`, 'warning');
    }
}

// إظهار إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#17a2b8'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// إغلاق نافذة المعاينة
function closePreviewModal() {
    document.getElementById('previewModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// طباعة المعاينة
function printPreview() {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// إنشاء محتوى الطباعة
function generatePrintContent() {
    const currentTime = new Date().toLocaleString('ar-SA');

    let content = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>موقف اتصالات المركزية</title>
        <style>
            @page { size: A4; margin: 20mm; }
            body {
                font-family: 'Cairo', Tahoma, Arial, sans-serif;
                margin: 0; padding: 0; direction: rtl; color: #333;
            }
            .print-header {
                text-align: center; margin-bottom: 30px;
                background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
                color: white; padding: 20px; border-radius: 10px;
            }
            .print-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .print-time { font-size: 16px; opacity: 0.9; }
            .devices-table {
                width: 100%; border-collapse: collapse; margin-top: 20px;
            }
            .devices-table th, .devices-table td {
                border: 2px solid #1da1f2; padding: 12px; text-align: center;
            }
            .devices-table th {
                background: #1da1f2; color: white; font-weight: bold;
            }
            .devices-table tr:nth-child(even) { background: #f8fcff; }
            .status-success { color: #28a745; font-weight: bold; }
            .status-danger { color: #dc3545; font-weight: bold; }
            .device-name { font-weight: bold; color: #2c3e50; }
            .footer {
                text-align: center; margin-top: 30px; font-size: 12px; color: #666;
                border-top: 1px solid #ddd; padding-top: 15px;
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <div class="print-title">📋 موقف اتصالات المركزية</div>
            <div class="print-time">📅 ${currentTime}</div>
        </div>

        <table class="devices-table">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>اسم الجهاز</th>
                    <th>حالة الاتصال</th>
                    <th>الملاحظات</th>
                </tr>
            </thead>
            <tbody>
    `;

    let rowNumber = 1;
    previewData.forEach(item => {
        if (deletedDevices.has(item.device)) return;

        const statusClass = item.status === 'تم الرد' ? 'status-success' : 'status-danger';
        const statusIcon = item.status === 'تم الرد' ? '✅' : '❌';
        const note = item.note || '-';

        content += `
            <tr>
                <td>${rowNumber++}</td>
                <td class="device-name">${item.device}</td>
                <td class="${statusClass}">${statusIcon} ${item.status}</td>
                <td>${note}</td>
            </tr>
        `;
    });

    content += `
            </tbody>
        </table>

        <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام راصم - جميع الحقوق محفوظة © ${new Date().getFullYear()}
        </div>
    </body>
    </html>
    `;

    return content;
}

// حفظ وإرسال
function saveAndSubmit() {
    // تحديث النموذج الأصلي بالبيانات المعدلة
    const form = document.querySelector('form');

    // إزالة الأجهزة المحذوفة من النموذج
    deletedDevices.forEach(deviceName => {
        const statusInputs = form.querySelectorAll(`input[name="status[${deviceName}]"]`);
        const noteInput = form.querySelector(`input[name="note[${deviceName}]"]`);

        statusInputs.forEach(input => input.remove());
        if (noteInput) noteInput.remove();
    });

    // تحديث البيانات المعدلة
    previewData.forEach(item => {
        if (deletedDevices.has(item.device)) return;

        const statusInputs = form.querySelectorAll(`input[name="status[${item.device}]"]`);
        const noteInput = form.querySelector(`input[name="note[${item.device}]"]`);

        // تحديث الحالة
        statusInputs.forEach(input => {
            input.checked = (input.value === item.status);
        });

        // تحديث الملاحظة
        if (noteInput) {
            noteInput.value = item.note;
        }
    });

    // إغلاق النافذة
    closePreviewModal();

    // إرسال النموذج
    showNotification('جاري حفظ البيانات...', 'info');
    setTimeout(() => {
        form.submit();
    }, 1000);
}

// إضافة أنيميشن CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>