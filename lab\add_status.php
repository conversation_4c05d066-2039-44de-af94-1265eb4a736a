<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("إضافة موقف اتصالات المركزية", true, "index.php");

// معالجة الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
    $statuses = $_POST['status'] ?? [];
    $notes = $_POST['note'] ?? [];
    $_SESSION['central_status_result'] = [
        'devices' => $devices,
        'statuses' => $statuses,
        'notes' => $notes,
        'time' => date('Y-m-d H:i:s')
    ];
    header('Location: confirm_send.php');
    exit;
}
?>

<style>
        body {
            background: linear-gradient(135deg, #232526 0%, #1e3c72 100%);
            min-height: 100vh;
            font-family: 'Cairo', Tahoma, Arial, sans-serif;
        }
        .central-status-section {
            background: rgba(255,255,255,0.95);
            border-radius: 22px;
            box-shadow: 0 12px 40px 0 rgba(30,60,114,0.15);
            padding: 40px 30px 35px 30px;
            margin: 48px auto 0 auto;
            max-width: 1000px;
            border: 2px solid rgba(29,161,242,0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        .central-status-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1da1f2 0%, #28a745 50%, #1da1f2 100%);
        }
        .central-status-section h4 {
            color: #1DA1F2;
            font-weight: bold;
            margin-bottom: 32px;
            letter-spacing: 1.5px;
            font-size: 2em;
        }
        .central-table {
            background: rgba(255,255,255,0.95);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(29,161,242,0.15);
            border: 2px solid rgba(29,161,242,0.3);
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        .central-table th {
            background: linear-gradient(135deg, #1da1f2 0%, #0d7ec7 100%);
            color: #ffffff;
            font-size: 1.2em;
            font-weight: 700;
            padding: 18px 15px;
            text-align: center;
            border: none;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }
        .central-table th:first-child {
            border-top-left-radius: 14px;
        }
        .central-table th:last-child {
            border-top-right-radius: 14px;
        }
        .central-table td {
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: 500;
            padding: 16px 12px;
            text-align: center;
            border: none;
            background: #ffffff;
            transition: all 0.3s ease;
        }
        .central-table tbody tr {
            border-bottom: 1px solid rgba(29,161,242,0.1);
        }
        .central-table tbody tr:nth-child(even) td {
            background: rgba(29,161,242,0.05);
        }
        .central-table tbody tr:hover td {
            background: rgba(29,161,242,0.1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(29,161,242,0.15);
        }
        .central-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 14px;
        }
        .central-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 14px;
        }
        .device-name {
            font-weight: 700;
            color: #1da1f2;
            font-size: 1.15em;
        }
        .status-options {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .form-check {
            display: flex;
            align-items: center;
            margin: 0;
        }
        .form-check-label {
            margin: 0 8px 0 5px;
            font-size: 1.05em;
            font-weight: 600;
            color: #34495e;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        .form-check-input {
            width: 18px;
            height: 18px;
            margin: 0;
            cursor: pointer;
            accent-color: #1DA1F2;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            border: 2px solid #1da1f2;
            border-radius: 50%;
            position: relative;
            transition: all 0.3s ease;
        }
        .form-check-input:checked {
            background: #1da1f2;
            border-color: #1da1f2;
        }
        .form-check-input:checked::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }
        .form-check-input:hover {
            border-color: #0d7ec7;
            box-shadow: 0 0 0 3px rgba(29,161,242,0.1);
        }
        .form-check:hover .form-check-label {
            color: #1da1f2;
        }
        .note-input {
            border: 2px solid rgba(29,161,242,0.2);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 0.95em;
            width: 100%;
            max-width: 200px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
        }
        .note-input:focus {
            outline: none;
            border-color: #1da1f2;
            box-shadow: 0 0 0 3px rgba(29,161,242,0.1);
            background: #ffffff;
        }
        .row-number {
            font-weight: 700;
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .btn-central {
            background: linear-gradient(90deg, #1DA1F2 0%, #28a745 100%);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 1.18em;
            font-weight: bold;
            margin-top: 32px;
            padding: 14px 0;
            box-shadow: 0 4px 16px rgba(29,161,242,0.18);
            transition: background 0.3s;
        }
        .btn-central:hover {
            background: linear-gradient(90deg, #28a745 0%, #1DA1F2 100%);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(29,161,242,0.25);
        }

        /* التوافق مع المتصفحات القديمة */
        @supports not (backdrop-filter: blur(10px)) {
            .central-status-section {
                background: rgba(255,255,255,0.98);
            }
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .central-status-section {
                margin: 20px 15px 0 15px;
                padding: 25px 15px 20px 15px;
                max-width: none;
            }
            .central-table th,
            .central-table td {
                padding: 12px 8px;
                font-size: 0.95em;
            }
            .status-options {
                flex-direction: column;
                gap: 8px;
            }
            .note-input {
                max-width: 150px;
                font-size: 0.9em;
            }
            .central-status-section h4 {
                font-size: 1.6em;
            }
        }

        @media (max-width: 480px) {
            .central-table {
                font-size: 0.85em;
            }
            .central-table th,
            .central-table td {
                padding: 10px 6px;
            }
            .device-name {
                font-size: 1em;
            }
            .form-check-label {
                font-size: 0.9em;
            }
            .note-input {
                max-width: 120px;
                padding: 6px 8px;
            }
        }

        /* تحسينات إضافية للأداء */
        * {
            box-sizing: border-box;
        }

        .central-table,
        .central-table * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>

<div class="central-status-section">
    <h4 class="text-center">📋 موقف اتصالات المركزية</h4>
    <form method="POST" action="")
        <table class="central-table">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>اسم الجهاز</th>
                    <th>حالة الاتصال</th>
                    <th>الملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $devices = ["ط2", "ط3", "ط4", "ط8", "ص15", "ص16", "ص17", "ص18", "س30", "س31", "س32", "س33", "س34", "س35", "ط12"];
                foreach ($devices as $index => $device) {
                    echo "<tr>";
                    echo "<td class='row-number'>" . ($index + 1) . "</td>";
                    echo "<td class='device-name'>" . htmlspecialchars($device) . "</td>";
                    echo "<td>";
                    echo "<div class='status-options'>";
                    echo "<div class='form-check'>";
                    echo "<input class='form-check-input' type='radio' name='status[" . htmlspecialchars($device) . "]' value='تم الرد' id='responded_" . $index . "' required>";
                    echo "<label class='form-check-label' for='responded_" . $index . "'>✅ تم الرد</label>";
                    echo "</div>";
                    echo "<div class='form-check'>";
                    echo "<input class='form-check-input' type='radio' name='status[" . htmlspecialchars($device) . "]' value='لا يوجد رد' id='no_response_" . $index . "' required>";
                    echo "<label class='form-check-label' for='no_response_" . $index . "'>❌ لا يوجد رد</label>";
                    echo "</div>";
                    echo "</div>";
                    echo "</td>";
                    echo "<td>";
                    echo "<input type='text' class='note-input' name='note[" . htmlspecialchars($device) . "]' placeholder='أدخل ملاحظة...'>";
                    echo "</td>";
                    echo "</tr>";
                }
                ?>
            </tbody>
        </table>
        <button type="submit" class="btn btn-central w-100">إرسال</button>
    </form>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>