<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("المخابر - الرصين", true, "dashboard/index.php");
include '../config/db.php';

// جلب إحصائيات المواقف
$total_status_count = 0;
$today_status_count = 0;
$pending_tickets_count = 0;

// التحقق من وجود جدول lab_status
$table_check = $conn->query("SHOW TABLES LIKE 'lab_status'");
if ($table_check && $table_check->num_rows > 0) {
    $res = $conn->query("SELECT COUNT(*) as total FROM lab_status");
    if ($res) {
        $row = $res->fetch_assoc();
        $total_status_count = $row['total'];
    }

    $res2 = $conn->query("SELECT COUNT(*) as today FROM lab_status WHERE DATE(created_at) = CURDATE()");
    if ($res2) {
        $row2 = $res2->fetch_assoc();
        $today_status_count = $row2['today'];
    }
}

// التحقق من وجود جدول tickets
$table_check2 = $conn->query("SHOW TABLES LIKE 'tickets'");
if ($table_check2 && $table_check2->num_rows > 0) {
    $res3 = $conn->query("SELECT COUNT(*) as pending FROM tickets WHERE sender_department = 'المخابر' AND status = 'مفتوحة'");
    if ($res3) {
        $row3 = $res3->fetch_assoc();
        $pending_tickets_count = $row3['pending'];
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
    .table {
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
    }
    .table th, .table td {
        vertical-align: middle;
        text-align: center;
    }
    .form-check-input {
        transform: scale(1.2);
        margin: 0 10px;
    }
    .btn-primary {
        margin-top: 20px;
        border-radius: 30px;
        font-size: 1.2em;
    }
    .central-status-section {
        background: rgba(30,33,36,0.95);
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31,38,135,0.25);
        padding: 32px 18px 24px 18px;
        margin: 40px auto 0 auto;
        max-width: 800px;
        border: 1px solid #222c3a;
    }
    .central-status-section h4 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 28px;
        letter-spacing: 1px;
    }
    .central-table {
        background: #181c22;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(29,161,242,0.08);
    }
    .central-table th {
        background: #23272b;
        color: #1DA1F2;
        font-size: 1.1em;
        font-weight: bold;
        border-bottom: 2px solid #1DA1F2;
    }
    .central-table td {
        color: #fff;
        font-size: 1.05em;
        background: #23272b;
    }
    .central-table tr:nth-child(even) td {
        background: #20232a;
    }
    .form-check-label {
        margin: 0 8px 0 2px;
        font-size: 1.08em;
    }
    .form-check-input {
        accent-color: #1DA1F2;
        transform: scale(1.25);
    }
    .btn-central {
        background: linear-gradient(90deg, #1DA1F2 0%, #28a745 100%);
        color: #fff;
        border: none;
        border-radius: 25px;
        font-size: 1.15em;
        font-weight: bold;
        margin-top: 28px;
        padding: 12px 0;
        box-shadow: 0 4px 16px rgba(29,161,242,0.15);
        transition: background 0.3s;
    }
    .btn-central:hover {
        background: linear-gradient(90deg, #28a745 0%, #1DA1F2 100%);
        color: #fff;
    }
</style>

<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= htmlspecialchars($_SESSION['user']['ar_name'] ?? $_SESSION['user']['username'] ?? 'المستخدم') ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم المخابر - نظام الرصين</p>

    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📊</div>
                    <h5>إجمالي المواقف</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_status_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📅</div>
                    <h5>مواقف اليوم</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $today_status_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #dc3545; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">🎫</div>
                    <h5>التذاكر المعلقة</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $pending_tickets_count ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>➕ إضافة موقف</h5>
                <p class="mb-3">سجل الموقف اليومي للوحدة</p>
                <a href="add_status.php" class="btn btn-outline-primary">ابدأ الآن</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 إرسال تذكرة</h5>
                <p class="mb-3">ابلغ عن مشكلة تحتاج الدعم الفني</p>
                <a href="send_ticket.php" class="btn btn-outline-success">إرسال تذكرة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📋 عرض المواقف</h5>
                <p class="mb-3">مراجعة المواقف المسجلة سابقاً</p>
                <a href="../dashboard/view_central_status.php" class="btn btn-outline-warning">عرض المواقف</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>⚙️ الإعدادات</h5>
                <p class="mb-3">إدارة الملف الشخصي والإعدادات</p>
                <a href="profile.php" class="btn btn-outline-info">الإعدادات</a>
            </div>
        </div>
    </div>

    <!-- قسم الوصول السريع -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom p-4">
                <h5 class="text-center mb-4" style="color: #1DA1F2;">🚀 الوصول السريع</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="add_status.php" class="btn btn-outline-primary w-100 py-3">
                            <div style="font-size: 2em;">📋</div>
                            <div>إضافة موقف جديد</div>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="send_ticket.php" class="btn btn-outline-success w-100 py-3">
                            <div style="font-size: 2em;">🎫</div>
                            <div>إرسال تذكرة دعم</div>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="my_tickets.php" class="btn btn-outline-warning w-100 py-3">
                            <div style="font-size: 2em;">📊</div>
                            <div>متابعة التذاكر</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الإحصائيات التفصيلية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card card-custom p-4 text-center">
                <h6 style="color: #28a745;">📈 إحصائيات هذا الشهر</h6>
                <div class="row mt-3">
                    <div class="col-6">
                        <div style="font-size: 1.5em; color: #1DA1F2;"><?= $total_status_count ?></div>
                        <small style="color: #aaa;">إجمالي المواقف</small>
                    </div>
                    <div class="col-6">
                        <div style="font-size: 1.5em; color: #ffc107;"><?= $pending_tickets_count ?></div>
                        <small style="color: #aaa;">التذاكر المعلقة</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card card-custom p-4 text-center">
                <h6 style="color: #17a2b8;">⏰ نشاط اليوم</h6>
                <div class="mt-3">
                    <div style="font-size: 2em; color: #28a745;"><?= $today_status_count ?></div>
                    <small style="color: #aaa;">المواقف المسجلة اليوم</small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>